------------------------------------------------------
Version 3.3.3
------------------------------------------------------
- Updated the description of the music disc to properly credit <PERSON><PERSON>.
- Added the "Gilded" Living Armour Upgrade, which pacifies <PERSON><PERSON> when equiped. Trained by giving Gold Ingots directly to piglins.
- Updated the death messages for dying to the Well of Suffering or the Sacrificial Knife.
- Deregistered the SPectral Block's item.
 -> Stop placing random things out of your test worlds! ;-;
- Modified some miscilaneous Curios stuff.
- Removed the fact that sacrificial damage would stop momentum.


------------------------------------------------------
Version 3.3.2
------------------------------------------------------
- Fixed Soft Coating and Fortuna Extract; voiding of drops is not part of the "ultimate price"!
- Fixed Routing Node text box so that it can be properly edited.
 -> Updated functionality; can now right-click the textbox when an item is selected in the filter to set the filter to "Everything".

- Updated Curios implementation.
- Fixed the guidebook not showing up in the Creative Tab and JEI.
- Removed some useless log spam.

------------------------------------------------------
Version 3.3.1
------------------------------------------------------
- Fixed crash that prevented the mod from working on servers.

------------------------------------------------------
Version 3.3.0
------------------------------------------------------
- Initial update to MC 1.20.x! Expect some breakage. You can thank/blame chonky for the release!
- Curios/Elytra-render checks no longer specifically check for ItemLivingArmor. 

------------------------------------------------------
Version 3.2.6
------------------------------------------------------
- Changed Sigil of Holding rendering so that it displays the selected Sigil in the hand.
- Added a new music disc, which is obtainable in the Mines!
- Modified several textures, and adjusted Reinforced Runes' recipes accordingly.
- Fixed the Harvest Moon ritual so that it works properly with Netherwart.
- Modified the recipe framework of the Mark of the Falling Tower to better facilitate creating custom recipes.
-> Now includes fluid support!

- Added Patchouli documentation for The Rhythm of the Beating Anvil ritual (Autocrafting Ritual).
-> Sorry it took a while. It was hard, Okay? - Wrince


------------------------------------------------------
Version 3.2.5
------------------------------------------------------
- Updated several entries in the guide book.
- Fixed it so that the Fishing Rod actually works.
-> Don't ask.

------------------------------------------------------
Version 3.2.4
------------------------------------------------------
- Implemented the "Pathway to the Endless Realm" ritual, which provides access to a larger version of the Demon Dungeons!
-> "The Endless Realm" is a sprawling, procedurally generated dungeon, filled with chests bursting with loot and monsters that pack a serious punch!
-> Those who successfully navigate its many chambers may find Demonite, an ore that cannot be found anywhere else. 
-> The dungeons currently have two distinct regions: 
->> The "Entrance Zone", which contains a wide array of loot and monsters
->> The "Hidden Mines", containing a bountiful reserve of Demonite, which is used for advanced alchemy and for empowering the Blood Altar to Tier 5. Accessible by finding the "Foreman's Key" in the Entrance Zone.
-> If you delve deep enough in the dungeon, a "Spatial Distortion" may occasionally form; this changes one of the doors within the room to one that leads to a rare dungeon room.
- More rooms, room types, and special rooms to come to the Dungeon (eventually (soon (tm)))!

- Added Demonite, which can be processed into Hellforged Sand and Hellforged Ingots.

- Added more advanced anointments, which offer either increased duration (XL) or a stronger effect (III).
- Added the "Standard Lengthening Catalyst" and "Standard Power Catalyst", which further augment potion effects in the Alchemy Flask.
- Added stronger Alchemic Reaction Chamber tools, which are both faster and longer-lasting than previous tools.
-> Hellforged Resonator
-> Hellforged Explosive Cell
-> Advanced Cutting Fluid
-> These tools also boost the chance for bonus outputs when used. For instance, an Intermediate Cutting Fluid will provide 1.5x Iron Sand per Raw Iron, whereas the Advanced Cutting Fluid provides 1.67x.

- Added upgraded upgraded Explosive charges and tweaked the power of previous versions.
-> Augmented Shaped Charge - breaks 7x7x7 cube of blocks.
-> Tunneling Shaped Charge - breaks 5x5x20 long set of blocks.
-> Augmented Controlled Charge - breaks up to 8x64 blocks of the same type.
-> Augmented Deforester Charge - breaks up to 8x64 logs, clearing away connecting leaves.
-> Augmented Fungal Charge - breaks up to 8x64 blocks related to Overworld and Nether big mushrooms.

- Added Hellforged Parts, which can only be found through loot within the Hidden Mines.
-> Used to reinforce Blood Runes, doubling the effect of the given rune.

- Increased the rate of "Tiny Corrupted Dust" when using the Resonator in the Alchemic Reaction Chamber.
-> Corrupted Dust can be used to increase the yield of the desired ore.

- Added a new feature to the Divination and Seer's Sigil: when sneaking and activating the sigil, a GUI will open which will allow you to drag individual HUD elements and save their new locations.

- Added new anointments:
-> "Repairing Salve", which repairs a damaged tool slightly when the tool is used.
-> "Void Essence", which destroys mundane blocks that are mined.
- Also tweaked "Miner's Secrets" to only work on exp-providing blocks, while also only being used up when additional exp is provided.

- Updated several textures related to vanilla ore processing.
- Fixed Blood Altar's delay after crafting. The delay between when an altar finishes a craft and attempts to start a new craft is now 30 ticks.
- Fixed Body Builder so that it is properly obtainable.
- Fixed Nether Wart handling for the Harvest ritual.
- Fixed the Sentient Scythe so that it wasn't made out of paper. Also actually does damage now!
- Allowed the Sentient Scythe to receive the Sharpness enchantment while enchanting.
- Changed the Ritual of the Feathered Knife to interact with the "Tough Palms" Living Upgrade.
- Added Amethyst Throwing Daggers, which do the same damage as Iron Throwing Daggers.
-> Can be combined in an ARC with a Lingering Alchemy Flask to add potion effects to the thrown daggers.

- Added the reworked ritual "Rhythm of the Beating Anvil", which can autocraft vanilla recipes. When augmented, can also simplify automation of Hellfire Forge and Alchemy Table recipes.
- Changed the "Penance of the Leadened Soul" so that it respects any currently held Training Bracelets.
- Reimplemented recipe for Obsidian Paths.


------------------------------------------------------
Version 3.2.3
------------------------------------------------------
- Fixed Fortuna Extract so that it properly applied its additional Fortune under certain conditions.
- Fixed a crash with the Teleposer that prevented Tile Entities from moving.
- Added smelting recipe for copper sand > copper ingot, and blasting furnace recipes for all dusts.
- Tweaked the recipe for the Primitive Resonator (has been renamed), as well as added intermediate tools for other ARC tools.

------------------------------------------------------
Version 3.2.2
------------------------------------------------------
- Fixed in-game guide for real this time.


------------------------------------------------------
Version 3.2.1
------------------------------------------------------
- Fixed in-game guide so that recipes properly load.
- Fixed non-crafting Alchemy Arrays.
- Fixed the Mark of the Falling Tower and Ritual of Magnetism to respect the current world's build limit.
- Changed a few textures such as the ore fragments.


------------------------------------------------------
Version 3.2.0
------------------------------------------------------
- Initial release of 1.18.2.
- Changed the ARC so that the texture changes based on the tool used and when it is active.
- Rebalanced some recipes due to Raw Materials dropping from base ores.
- Added copper ore processing.
- Added Glowberries and Berry bush handling to the Reap of the Harvest Moon.


Known issues
- Ingame guide is broken in a few places, primarily when viewing mod recipes such as Blood Altar crafting..
- Life Essence blocks do not have the proper overlay when submersed in liquid.


------------------------------------------------------
Version 3.1.11
------------------------------------------------------
- Reworked and implemented the Potion Flask system! Quite a bit different than previous versions, so make sure to check the guide.
- New (and old) non-Vanilla potion effects include:
-> Bounce
-> Gravity
-> Grounded
-> Suspended
-> Heavy Heart
-> Passive
-> Spectral Sight
-> Hard Cloak
-> Obsidian Cloak
-> Flight

- Fixed server crash for the Reverence of the Condor ritual.
- Reimplemented Teleposers. Hurray! 
- Reimplemented the Teleposition Sigil.
- Reimplemented the Sigil of Suppression.
- Reimplemented the Demon Pylon.
- Added Will Catalysts, which can be applied to Demon Will Clusters to dramatically increase their rate of growth.
- Changed the Hellfire Forge so that it can use Aspected Will in crafting operations.
- Modified the Ritual Diviner so that it can cycle through the ritual list backwards by left-clicking in the air.
- Added a new item, "Basic Routing Logic Upgrade", which increases the total throughput of the Master Routing Node's system.

- Reimplemented several rituals:
-> Crash of the Timberman
-> Dawn of the New Moon
-> Ritual of the Satiated Stomach
-> Speed Ritual
-> Ritual of the High Jump
-> Laying of the Filler
-> Convocation of the Damned (reworked)
-> The Sinner's Burden (renamed from "Ritual of Grounding")
-> Mark of the Falling Tower (New meteor system! Oooh, sparkley~!)

- Added a new ritual, Yawning of the Void. Used to completely clear large areas of blocks.

- Tweaked recipe for Frame Parts to make them cheaper.

------------------------------------------------------
Version 3.1.10-32
------------------------------------------------------
- Fixed recipe for the "Brilliance" Living Upgrade.

------------------------------------------------------
Version 3.1.10
------------------------------------------------------
- Fixed server-sided issue that prevented recipes from loading for certain types.


------------------------------------------------------
Version 3.1.9
------------------------------------------------------
- Added the missing recipes for a couple of the Living Armour Downgrades.
-> Yes, Way is indeed blind.


------------------------------------------------------
Version 3.1.8
------------------------------------------------------
- Made Patchouli a required library.
- Reimplemented the Penance of the Leadened Soul ritual. Operates slightly differently than before.
- Reimplemented the following Living Armour Downgrades:
-> Storm Trooper
-> Battle Hungry
-> Quenched
-> Dulled Blade
-> Weakened Pick
-> Diseased
-> Crippled Arm
-> Limp Leg
- Reimplemented Repairing
- Added the following Living Armour Upgrades/Downgrades:
-> Brilliance (Increases armour and toughness values of Living Armour)
-> Concrete Shoes (Decreases swim speed)

- Decreased hardness of shaped charges.

Known issue: Desync between the server and client for the Living Armour chestpiece under certain conditions. Occurs when the item is shift-clicked out of the player's chestpiece slot. Does not occur when clicked without shift.
- Only causes a ghost item to appear in the client's inventory. Does not otherwise affect the performance of the armour.

------------------------------------------------------
Version 3.1.7
------------------------------------------------------
- Fixed another crash with the Living Armour. Maybe I got it this time!


------------------------------------------------------
Version 3.1.6
------------------------------------------------------
- Increased range of the Tau plant to a 3x1x3 block range centered on the plant. Thus makes it easier to farm the Saturated version.
- Fixed crash with the Living Armour under certain conditions.
- Implemented a fix to the Sacrificial Knife so that it respects gravestone mods and other effects that occur on-death.
- Fixed the Dagger of sacrifice so that it similarly respects death events.

------------------------------------------------------
Version 3.1.5
------------------------------------------------------
- Fixes client-sided crash for the Alchemy Table when on servers.
- Made the Water Sigil not work in the nether (still drains LP on use).

------------------------------------------------------
Version 3.1.4-24
------------------------------------------------------
- Way forgot to reenable the "Edge of the Hidden Realm" ritual. Now he has to make a new build.

------------------------------------------------------
Version 3.1.4
------------------------------------------------------
- Added the Demon Dimension, with Dungeons! To be transported to a dungeon in this dimension, use the ritual "Edge of the Hidden Realm".
- Added new Tau fruit (and its saturated version), which are found in the new dungeons. These are required to progress to T4.
- Added new, longer lasting anointments, as well as more powerful versions.
- Fixed a bug when removing max level upgrades from Living Armour.
- Fixed a client-server desync with the item filter count.
- Added an audible and visual indication for when an anointment on a tool runs out.
- Included warning flags in the Alchemy Table GUI that will state if there is not enough LP or if the orb is unbound/the wrong tier.
- Added the Elytra Living Armour upgrade! Elytra not included.
- Added the Training Bracelet for the Living Armour! Now you can train the upgrades you want to!
- Increased the durability of the Living Armour to diamond level, but allowed all non-chestpiece pieces to break.

------------------------------------------------------
Version 3.1.3
------------------------------------------------------
- ARC now will consume less durability for tools with Unbreaking. Durability is consumed at (1/(unbreakingLevel+1)) chance.
- Fixed fluid capabilities of the ARC.
- Made it so that the textbox of the Filters can only accept numbers. Also allowed the "Delete" key to delete values from the text field.


------------------------------------------------------
Version 3.1.2
------------------------------------------------------
- Reimplemented Routing Nodes
-> Has a complete UI overhaul. Check the guide for details!
- Added Curios support
- Added recipes for water and lava buckets using their respective Sigils (as well as a few other uses of Sigils inside the Alchemy Table)
- Fixed Gift of Ignis not lowering its cooldown when not on fire.
- Allowed the Divination Sigil and the Seer Sigil's HUD element to work in both the offhand and the Sigil of Holding. Also allows the Demon Aura Gauge to work in the offhand (why you'd have it in your offhand, I can't say).
- Added the configs! Yay!
- Added the Ritual "Call of the Zephyr".


------------------------------------------------------
Version 3.1.1
------------------------------------------------------
- Fixed the Throwing Daggers so that they act as the appropriate type.
- Fixed the Sigil of Magnetism's range so that it is centered on the player.
- Fixed the Living Armour upgrade "Tough" so that it is now obtainable. Now you can get hurt for an actual cause!
- Fixed an NPE crash due to the Ritual of Living Evolution. Crash occurred if a player equipped a newly crafted Living Armour set while on the activated ritual.
- Changed the behaviour of the Living Upgrade Tomes. 
-> Tomes now will attempt to apply their contained EXP (not levels) regardless of current or tome levels. 
-> If exp from tome would put you pass the armour's point cap, exp is instead added up to the maximum possible for said Living Upgrade minus one. 
-> Exp applied to the Living Armour is syphoned from the tome, and the remaining exp is not lost.
-> If the remaining exp on the Living Upgrade tome is below the amount required for at least one level of its respective upgrade, the tome is destroyed.
- Waterlogged blocks now count as "water" for the Incense Altar.
- Added the recipe for the Demon Will Aura Gauge.
- Reverted capacity formula for the Blood Altar.
- Fixed duplication glitch for the Alchemical Reaction Chamber.


------------------------------------------------------
Version 3.1.0
------------------------------------------------------
- Majorly refactored the progression of the mod. Instead of starting with a Snare, you instead start by crafting the Blood Altar. The changes are documented in the guide, and you can follow its "Getting Started" entry!

- Changed the tooltips so that they are gray, more easily differentiating them from the name of the item.
- Added the Sigil of Holding
- Changed the crafting of Tartaric Gems so that you no longer need to use the previous tier gem in the gem slot.
-> The Hellfire Forge will now syphon from the gem in the crafting table first, and all unused will from the consumed gem will be placed in the crafted gem.

- Changed the GUI for the Alchemy Table
-> You can now select which sides that an input/output slot is accessible from. Click on the slot you wish to change, then click on the directional toggle buttons.

- Fixed Plunderer's Glint not properly applying the Looting level.
- Also fixed a NPE crash due to Iron Tip. Fixes some crashes due to mods using the ItemUsedFinish event.

- Fixed the Blood Altar not being able to input fluids. About time, Way!
- Added new Explosive Charges
-> Fungal Charge, which is very useful for giant mushrooms and nether mushrooms
-> Controlled Charge, which will destroy only blocks that match the block it is placed on.
- Added the ability to apply a few select Anointments to the charges. Only one can be applied to a charge at a time.
-> Soft Touch, Fortuna, and Smelting

- Fixed the Living Armour so that you cannot use an upgrade tome to usurp the point cap.
-> Fixed "Strong Legs" so that it no longer runs the program "CrunchyLegs.exe" - as a result, you no longer suffer fall damage from jumping on the same level. 
-> Removed the direct fall damage mitigation from "Strong Legs"

- Added two types of Throwing Daggers to the Blood Mage's offensive kit.
-> Iron Throwing Dagger
-> Syringe Throwing Dagger

- Refactored the guide so that it provides +2 to awesomeness.


------------------------------------------------------
Version 3.0.6
------------------------------------------------------
- Reimplemented the Well of Suffering.
- Added several new items, called "Anointments"! These are items that provide temporary buffs to your tools and weapons, such as silk touch, higher damage, and more! Look them up in the Utility section of the book. These include: 
	- Honing Oil
	- Soft Coating
	- Fortuna Extract
	- Holy Water
	- Miner's Secrets
	- Dexterity Alkahest
	- Iron Tip
	- Plunderer's Glint
	- Slow-burning Oil

------------------------------------------------------
Version 3.0.5
------------------------------------------------------
- Fixed a crash with the Blood Altar because I am dumb.
- Also fixed the recipe for the Day array. Now you don't need to clean your clocks!

------------------------------------------------------
Version 3.0.4
------------------------------------------------------

- Added two new explosives:
	- The Shaped Charge, which will break a 5x5x5 volume when placed
	- The Deforester Charge, which can chop down a tree up to a maximum of 128 logs.
	- (Both explosives drop all blocks affected, and do not cause further environmental nor entity damage.)
	
- Added new alchemy arrays:
	- Two arrays, which changes the current daylight cycle to day and night. Recipes are temp.

- Fixed the following Living Armour upgrades so that they are now obtainable:
	- Experienced
	- Body Builder
	
- Fixed Living Armour not upgrading under certain conditions.
	
- Fixed the two rituals involving Living Armour so that they are properly activatable using a Weak Activation Crystal.
	
- Fixed NPE in Blood Altar when trying to interact with the contained Fluids
- Also fixed bug that prevented the Altar from accepting fluids piped in as inputs.
- Fixed a client-side crash when right clicking the ground with a Lava Crystal when on a server - now properly makes a fire without consuming the client.
 
- Fixed the JEI so that it no longer ouputs an error when loading Alchemy Array recipes without a crafting output.
- Fixed crash with Actually Additions


------------------------------------------------------
Version 3.0.3
------------------------------------------------------

- Updated the Sanguine Scientiem - it's very well written! Special thanks to both VT-14 and Wrince for their long hours of effort to make this book better than I could have done!
- Fixed a crash with the Living Armour when crafted and worn right after enchanting. 



------------------------------------------------------
Version 3.0.2
------------------------------------------------------

Readded the Living Armour. Currently only with a few upgrades, and no downgrades.
	- Pin Cushion
	- Soft Fall
	- Tough
	- Strong Legs (Now can be temporarily deactivated when jumping by sneaking)
	- Healthy
	- Experienced
	- Charging Strike
	- Tough Palms
	- Quick Feet
	- Poison Resistance
	- Gift of Ignis
	- Dwarven Might
	- Body Builder

Reimplemented the following rituals:
	- Resonance of the Faceted Crystal
	- Crack of the Fractured Crystal
	- Reap of the Harvest Moon
	- Ritual of the Shepherd
	- Ritual of the Green Grove
	- Focus of the Ellipsoid
	- Sound of the Cleansing Soul
	- Ritual of Living Evolution

------------------------------------------------------
Version 3.0.1
------------------------------------------------------

- Reimplemented the Incense Altar with all appropriate blocks.
- Added guide entries for the Incense Altar and Blood Altar. Other misc entries added, too.
- Fixed a server-related crash on the client on the Sigil of the Green Grove.
- Added ore processing to Ancient Debris.
- Readded the other Sentient Tool types (with one more on the way).
- Added a few WIP items that cannot be used yet - they're part of the D*$)@ D*#@(@* system that I'm adding for Tier 4.
- Added the API - It's still in flux, so expect it to change very soon!
- Probably forgot something important.
- Fixed a crash for the Lava Crystal that made it blow up the Client if used on a server. No, that was not the intended purpose!
- Added the Ritual Tinkerer for Rituals.
- Added the "Tome of Peritia" again - stores EXP.


------------------------------------------------------
Version 2.4.3
------------------------------------------------------

- Fixed Lava Crystals causing crash when used while not yet bound
- Fixed Teleposers sending entities at the destination position the wrong way
- Fixed Speed Runes not working on Tier 2 altars
- Fixed crash when checking tasks for some entities
- Fixed crash when checking some entities for potion effects
- Re-implemented the Boost potion
    - The PR still had TODOs in the description when I merged so this may not be complete yet

------------------------------------------------------
Version 2.4.2
------------------------------------------------------
Now with no guarantees for working textures!

Blood Altar:
    - A Blood Altar with a Redstone Lamp below it will now emit a redstone signal upon finishing crafting (#1635)
    - Fixed Blood Altar being able to obtain negative progress (#1649)

Living Armor:
    - Fixed Storm Trooper Level 4 costing 65 instead of -65 upgrade points (#1631)

Rituals:
    - Fixed Ritual of the Eternal Soul (Ritual Stone layout & didn't work) (#1633)
    - Fixed Ritual Tinkerer (ritual area is now unique per ritual instance), added the ability to reset the ritual ranges to default (#1636)

Misc:
    - Lava Crystals can now set things on fire (#1652)
    - Fixed parts of the German translation. Other translations still need to be reworked to accomodate changed translation keys (#1640)
    - Fixed Guidebook showing wrong recipes (Rune of Self-Sacrifice instead of others) (#1641)
    - Wooden Paths now behave like wood instead of stone (sound & efficient tool) (#1638)


Technical Stuff:
    - Fixed Veil of Evil and Ward of Sacrosanctity console spam (which could eventually lead to an out of memory exception) (#1639)
    - Fixed GLSL shader compilation errors (affected Mesa driver (AMD GPU) on Linux) (#1624)
    - Fixed `/bloodmagic network` command permissions (#1613)
    - Fixed a rare crash that could occur when summoning mobs (#1618)

------------------------------------------------------
Version 2.4.1
------------------------------------------------------

Nut was too lazy to create a changelog, it's AEon's turn now!
Changes made from Feb 02 - now (Fill end date in later)


Living Armor:
    - Fixed "Soft Fall" not preventing damage
    - "Nocturnal Prowess" should not flash anymore
    - "Trickshot" now ignores HurtResistanceTime
    - "Trickshot" now works properly with sentient arrows
    - "Step Assist" does not take effect while sneaking

Sentient Bow:
    - Fixed Destructive Will arrows exploding continuously
    - Fixed Tipped Arrows getting improved even if no Will can be used

Sigils:
    - Buffed "Sigil of the Fast Miner" to be in accordance to the "Sanguine Scientium"
    - Fixed a bug where "Sigil of the Fast Miner" would also cause the whirlwind effect
    - Added "Whirlwind" to the potion registry
    - Fixed broken Sigil tooltip formatting
    - Fixed Sigil of Holding not updating some Sigil data, allowing for exploits
    - Greatly improved "Sigil of Compression" performance
    - Fixed "Sigil of Compression" bug that would eat leftover items (usually everything between 55 and 64 items for a 3x3 compression)


Rituals:
    - Ported Veil of Evil & Ward of Sacrosanctity
    - Fixed "Serenade of the Nether" replacing lava source blocks with lava (fixed underlying function to detect flowing liquids)
    - Added configurability to Ritual of the High Jump by modifying a new ritual area with the Ritual Tinkerer

Misc:
    - Fixed routing node oredict filter (crashes)
    - Fixed mimic vanishing in some cases
    - Fixed "Blood Letter's Pack" and "Coat of Arms" always being used in the main hand slot (action bar/hotbar)
    - Stop insertions into Demon Crucible if the inventory is not empty
    - Fixed abnormal deaths at your own hands through the sacrificial dagger (hopefully) (Gravestone bugginess)
    - Fixed "Unmending" mod incompatibility
    - Fixed Teleposers crashing the game if someone force-fed it junk
    - added Nut being angery at contributors for messing stuff up
    - Added some Russian language strings for the guide book
    - Added repair recipes for Sentient Tools & Living Armor to JEI
    - Many translation keys have changed which makes affected translations fall back to english

Technical Stuff no player cares about:
    - Formatted Project code
    - Fixed BlockState parsing (mimic vanishing)
    - Fixed some and streamlined ritual block ranges
    - Mystery drain still mysterious
    - Use TextFormatting rather than color codes in block lang names
    - Fixed world unload crash (may happen if a world is loaded by world generators or JEI Resource graphing)
    - Fixed `/bloodmagic network get` command
    - Added translation keys for tickethistory command help strings

------------------------------------------------------
Version 2.4.0
------------------------------------------------------

It's been a while, eh? Some of you may be asking yourself "whats up with bloodmagic" like that damned recon on a certain
social media site. Well here's what's up: put something snarky here when done writing changelog

There's so much here, I'm probably going to miss some. Here's a general overview from just skimming commit titles.

Much of this was done via PR because I'm ~~lazy~~ too busy having fun with Fabric.

- Fixed harvest moon not working with pumpkins
- [API] Added a way to unregister an altar component
- Moved anything related to Tier 6 behind an opt-in config. Please stop asking me how to get it.
- Fixed small demon stone bricks recipe so it returns 4 instead of 1
- Ported some old rituals from 1.7
    - Cry of the Eternal Soul
    - Reverence of the Condor
    - Ritual of the Feathered Earth
- Added a new ritual, Ritual of Grounding
- Optimized bound tool harvesting
- Fixed render issues with demon crystals
- Fixed Hymn of Syphoning not updating some tanks visually
- Added very basic documentation for several rituals to the guide book
- Made meteor costs for Mark of the Falling Tower configurable in the meteor json
- Fluid related sigils should now work identically to buckets of the fluid
- Fixed ritual tinkerer area setting behaving wonkily
- Fixed Seer Sigil not working for incense altars
- Added the ability to buff movement and updraft arrays by providing more of their ingredient
- Ported the ritual dismantler from 1.7.10
- Logic fix for the Solar Powered
    - Now it requires a clear line of sight to the sky as well as it being day time
- Fixed Sigil of Magnetism AOE being offset from the player
- Rewrote commands
    - They're different in some way, some how
    - Don't ask me how
- Quality of life improvements for the ritual tinkerer
- Changed the activation crystal levels of some rituals to use the awakened crystal
    - Mark of the Falling Tower
    - All the living armor ones
    - It's late and I'm tired don't hate me for being too lazy to look up their names
- Fixed up some bounding boxes for some blocks with fancier models
- Sentient Bow now properly handles modded and potion-ified arrows

I can't wait for this to turn out to be very broken so I can yell at the contributor :>

------------------------------------------------------
Version 2.3.3
------------------------------------------------------

- Fixed Altars not filling orbs of their own tier

------------------------------------------------------
Version 2.3.2
------------------------------------------------------

looky bois, we beta now

- Fixed Sentient Sword attacking mobs and players without being provoked
- Fixed weird Crystal Clusters being weird
    - They are now less weird
- Fixed Ritual Reader and Tinkerer not being in the creative tab
- Fixed an NPE that would happen with some blocks in the Ritual of Magnetism's area of effect
- Fixed Mimics not being able to replace blocks during placement
- Fixed issues with inter-dimensional teleportation
    - Teleposers and Teleposition Sigils are now fully functional
- Fixed custom arrows not having their effects when fired from the Sentient Bow
- Fixed Splash Potions being unthrowable while Living Armor had the quenched downgrade
- Fixed Ritual Stones being considered Iron instead of Rock
- Fixed the Lava, Water, and Void sigils so they now work properly with tanks
- Fixed an issue where the Teleposer would attempt to damage an offline player
- Fixed the Compression Sigil so it now functions
- Fixed low tier altars being able to fill high tier orbs
- Fixed Sentient Tools having the wrong material
- Fixed Sentient Tools not having a repair material
    - It is now demon crystals
- Added a config to disable mob drops from mobs killed by the Well of Suffering
- Added an additional glow to the Dagger of Sacrifice when it has a full incense bonus
- Reworked the Phantom Bridge
- Updated german translation
- Updated chinese translation

------------------------------------------------------
Version 2.3.1
------------------------------------------------------

- Fixed Ritual of Magnetism not being registered
- Fixed Mob Sacrifice Array so it no longer kills bosses and players
- Fixed Will type serializer using lowercase names
- Fixed item rendering for the Sigil of Holding HUD
- Added mod id to command localization keys to prevent potential conflicts

------------------------------------------------------
Version 2.3.0
------------------------------------------------------

- Fixed Basic Cutting Fluid recipe so it no longer requires an unobtainable potion
- Fixed Teleposer Blacklist not working
- Fixed the bounding box on the Blood Tank
- Fixed crafted Inscription Tools not having their uses tag applied
- Fixed JEI queries with bound Blood Orbs
- Fixed Crushing Ritual not passing a valid player
- Fixed Mending applying to Potion Flasks
- Fixed Teleposition Sigil not checking for a Teleposer at it's destination
- Fixed Lava Crystal not syphoning LP
- Fixed the Laputa array moving unbreakable blocks
    - ie: Bedrock
- Fixed the Ritual of the Green Grove not working on Cactus and Reeds
- Added a Mob Sacrifice array
- Updated the HUD system
    - You can now edit the HUD layout via the in-game config editor with a drag'n'drop system.
    - This screen is only available while in a world.
- Updated the German translation
- Updated the Chinese translation
- [API] Reworked how rituals are registered
    - This will break any addons that work with rituals
    - If you report an issue to me regarding broken addons, it will be closed without any comment

------------------------------------------------------
Version 2.2.12
------------------------------------------------------
- Fixed Hellfire Forge and Alchemy Table's shape-crafted-ness.
- Fixed Cutting Fluids so they now work properly.
- Fixed the Fluid filters so that they now work perfectly in the routing system.
- Fixed the Living Armour Upgrade that provides night vision, so it is no longer as finicky.
- Proved that Way could update the version number properly without being told by the maid.

------------------------------------------------------
Version 2.2.11
------------------------------------------------------
- Fixed the Alchemy Array from voiding items when broken when it shouldn't have.
- Fixed Blood Tank NBT transfer between item/tile form.
- Made sure to not offend TehNut's sensibilities :NutMaid:

------------------------------------------------------
Version 2.2.10
------------------------------------------------------
- Fixed Turret Array crash on servers.
- Fixed the Blood Altar so it can actually fill Blood Orbs (derp).

------------------------------------------------------
Version 2.2.9
------------------------------------------------------
- Fixed stupid NPE in the Furnace Array
- Fixed various issues with the Alchemy Table:
	- Now works for recipes that require an LP cost.
	- Hoppers no longer lose items when items are placed into the "slave" Alchemy Table who has some slots restricted.
- Added an entry to the book that explains you can, in fact, use Sea Lanterns instead of glowstone blocks for your Tier 3 altar.
- Fixed the Demon Will crystals growing when they shouldn't. Also lowered the time between natural crystal growths in Will-enriched areas.
	- Side note: who's bright idea was it to have to wait 15 minutes per crystal growth?
- Added the "Resonance of the Faceted Crystal", which can be used to create your first aspected Will crystal clusters.
- Made it so the Crystallizer no longer generates a random aspected Will crystal cluster.
- Fixed rare crash with the Blood Altar, which only has a chance of occuring on restart.

------------------------------------------------------
Version 2.2.8
------------------------------------------------------
- Fixed a client side null-pointer exception with the Blood Lamp
	- It's a bright idea to fix this as soon as I can.
- Changed the recipe of the Teleport Array:
	- Note from Scotty: Captain, I'll remind ya what happened last time you put an apple in her array! Use an Enderpearl and redstone dust next time!
- Added new arrays
	- The Turret Array:
		> Place an array on top of an inventory with arrows and then place a bow and an arrow in the array. The array will target enemies greater than 3 blocks away and less than 32, using any arrows in the inventory.
	- Spike Array:
		> Place a piece of cobblestone and iron ingot in the array. The array deals damage to any living entity that enters
- Increased the max number of items transferable by the Master Routing Node in its system to 64 per second. Will revisit this limit if I figure out a less silly upgrade system.
- Added additional effects to the Sentient Bow when aspected to different Will types.
- Added in book entries for the Teleport Array and the Turret Array.
- Fixed the Haste sigil and "Quick Feet" so that they work with MC's new movement method.
- Removed added health from "Quick Feet" - seriously, why was this a thing?
- Readded the ritual "Focus of the Ellipsoid": creates a hollow ellipsoid from blocks provided in the connecting chest.
	- Note: The dictionary definition for "Ellipsoid" is a three-dimensional figure whose plane sections are ellipses or circles. For those who weren't born in a math class, it means it is a sphere that has different radii in each direction.
- Fixed an off by one in altar upgrade management causing the first of any upgrade to not be counted
- Fixed the LP cost of the Master Blood Orb
    - This bug has gone unnoticed for 2 years now.
- Potion Flasks can be crafted now
- Fixed a server crash when handling dye recipes
- Moved several text outputs out of the chat and into the action bar
- Fixed a crash when trying to use unbound toggle-able sigils in the Sigil of Holding
- Fixed an issue where unbound sigils could be used inside the Sigil of Holding
- Added missing items for Demon Crystals and Demon Pylons

------------------------------------------------------
Version 2.2.7
------------------------------------------------------
- Added the Furnace Array:
    - Name pending.
    - Takes health from nearby players to power adjacent furnaces.
    - Not covered by standard medical insurance.
- Added a new Teleport Array:
    - Please keep all hands and feet inside the floating circle until you have come to a complete stop.
    - Note to Scotty: Feed the non-floating circle a Feather followed by an Apple to initiate the Beaming sequence.
- Seer Sigil should now properly mimic all functions of the Divination Sigil.
- Fixed some console error spam when using Lava/Water/Void sigils.
- Registered Sulfur (dustSulfur) and Saltpeter (dustSaltpeter) to the ore dictionary.
- Rewrote how IBindable stores it's data:
    - This will probably break existing worlds if you have Rituals running. Stop them before updating.
    - This will also unbind any of your bound items. Bind them again after updating.
    - *glances at the big red A next to all 1.12 files*
- Fixed Haste being *constricted* when above level 2.
- De-nerf Fast Miner array so it provides Haste 3 again.
- Prepped API for recipe tweaking mods.

------------------------------------------------------
Version 2.2.6
------------------------------------------------------

- Fixed a weird thing where slates were weird
- Added HUD elements for Tranquility and Inversion
- Fixed Sigils stacking 64 times higher than they should have

As a side note, I've been listening to http://loli.dance/ for the last like 2 hours now.

------------------------------------------------------
Version 2.2.5
------------------------------------------------------

- We are now even more sure we're looking at a block before doing blocky things
- Fix Lava Crystal being consumed by furnaces
- Augmented Capacity and Self Sacrifice runes remember their names now
- Satiated Stomach will now let you gorge yourself on overly high saturation foods
    - I'm looking at you, Pam. Stop being weird and giving your foods 28 saturation.
- Made Mod ID node filter dumber so it actually, like, works properly
- "Temporarily" disabled cross-dimensional teleports for entities. Temporarily.
    - Did I mention this is temporary? As in totally not permanently?
    - Who am I kidding? I'll probably forget about this.
- Bounce Arrays are no longer death traps when sneaking.

------------------------------------------------------
Version 2.2.4
------------------------------------------------------

- Make sure we're looking at a block before doing blocky things
- Slightly altered the altar's renderer
- Added new spammy logging with config options
    - Prints out information like mods registering things via the API and stuff

------------------------------------------------------
Version 2.2.3
------------------------------------------------------

- Fixed mundane components being really really really thirsty
    - (everything required water reagents. everything.)
- Buffed the Fast Miner array with a nerf
    - Vanilla doesn't like Haste 3 I guess?
- Moved Divination Sigil altar output to a HUD element
    - RIP Blood Altar chat spam 2014-2018
- Mark of the Falling Tower is now re-opened and ready for sacrifices.
- Polished Demon Stones are no longer lonely uncraftable bois
- Somewhat more efficient (at least LoC wise) altar upgrade checking
- Blood Altar is now less stingy about outputs
- Coerced augmented Potion Flask recipe outputs out of hiding

------------------------------------------------------
Version 2.2.2
------------------------------------------------------

- Fixed Alchemy Arrays so they might actually function now. Might.
    - How did this even function properly before? I have no idea.
- Soul Snare should now stop giving the thrower ghosty debuffs. Should.
- Blood Magic is now in the mood to launch on servers. Probably.
- Soul Forge crafting works again! Potentially!
- Fixed the altar nuking any and all NBT attached to output items.
- Fixed the version not being replaced properly
- Rewrote Hwyla integration.
    - Should now function a lot nicer.
- Implemented a new API for recipe registration
    - Still need to move the alchemy table over, but otherwise this seems to function just fine.
- Implemented a new API for registering tranquility blocks.
    - Will allow Chisel to stop crashing

------------------------------------------------------
Version 2.2.1
------------------------------------------------------

- Fixed crashes when using systems that depended on internal entity names
- Fixed a crash when attempting to get the owner from unbound bindable items
- Fixed a server crash on startup
- Fixed GUIs not displaying the background and tooltips properly
- Helped the Sentient Sword get over it's identity crisis
- By the way I'm not actually upset or anything guys.
    - Please stop overreacting Reddit.

------------------------------------------------------
Version 2.2.0
------------------------------------------------------

- Basic port to 1.12
    - Does not include the vast majority of rewrites and cleanup I did on the other branch
    - Completely untested. Done out of spite. Go away.
    - There, you have it. Screw off now.

------------------------------------------------------
Version 2.1.11
------------------------------------------------------

- Added support for some modded crops (Untested. Theoretically they work)
    - Harvestcraft
    - Actually Additions
    - Extra Utilities
- Fixed permission level for sub-commands
- Large Bloodstone Bricks should receive 4 from a craft
- Fixed Diamond Meteor giving blocks of diamond
- Dynamically calculate Meteor Weight
- Fixed NPE in fluid filter
- Fixed Blood Tank capacity being reset when the chunk reloaded
- Updated russian translation
- Fixed Rune Type capability not registering on physical client
- Updated Sigil of Holding texture so set color is more visible
- Fixed blocks destroyed by bound tools not storing any NBT data
- Fixed Harvest ritual not working on Melons and Pumpkins
- [API] Methods for removing recipes

------------------------------------------------------
Version 2.1.10
------------------------------------------------------

- Fixed malicious clients being able to load arbitrary chunks on the server
- Fixed Bound tools not setting their tool class and harvest level

------------------------------------------------------
Version 2.1.9
------------------------------------------------------

- Fixed Alchemy Table overwriting output each time it crafted something
    - https://github.com/WayofTime/BloodMagic/issues/1119
- Blood Magic commands (/bloodmagic) can now be used in Command Blocks
    - https://github.com/WayofTime/BloodMagic/issues/1117
- Re-implemented the Soul Fray debuff on death
    - No more exploiting sacrifice by killing yourself over and over (again)
    - https://github.com/WayofTime/BloodMagic/issues/1118
- Updated Traditional Chinese localization
    - https://github.com/WayofTime/BloodMagic/pull/1116

------------------------------------------------------
Version 2.1.8
------------------------------------------------------

- Fixed more null stack issues
    - Will it ever end?
- Fixed a crash when activating a ritual via Redstone
- Fixed a fluid handling issue with Blood Tanks
    - https://github.com/WayofTime/BloodMagic/issues/1108
- Fixed issue where Sigils inside a Sigil of Holding were using the wrong item
    - https://github.com/WayofTime/BloodMagic/issues/1102
- Fixed crash when activing Bound Tools
    - https://github.com/WayofTime/BloodMagic/issues/1114
- Updated the Chinese localization
    - https://github.com/WayofTime/BloodMagic/pull/1052
- Placer Ritual is now much more performant
- Many recipes have been moved over to support the Ore Dictionary
    - https://github.com/WayofTime/BloodMagic/issues/1101

------------------------------------------------------
Version 2.1.7
------------------------------------------------------

- Fixed Grave Digger not applying damage bonus
    - https://github.com/WayofTime/BloodMagic/issues/1098
- Fixed crash when attempting to handle the dropping of Demonic Will
    - https://github.com/WayofTime/BloodMagic/issues/1091
- Fixed Crash of the Timberman dupe
    - This also adds support for IItemHandler based inventories
    - https://github.com/WayofTime/BloodMagic/issues/1090
- Fixed a crash when a Meteor struck the ground
    - https://github.com/WayofTime/BloodMagic/issues/1088
- Fixed the Seer's Sigil not using Roman Numerals to display the altar tier
    - https://github.com/WayofTime/BloodMagic/pull/1094

------------------------------------------------------
Version 2.1.6
------------------------------------------------------
- Fixed invisible Sentient Specters destroying everything and everybody around their owners
    - lol
    - https://github.com/WayofTime/BloodMagic/issues/1065
- Fixed occasional crash when Blood Altar checked it's recipe
    - https://github.com/WayofTime/BloodMagic/issues/1086
- Fixed Blood Light acting as a collidable block in obfuscated environments
    - I think
    - https://github.com/WayofTime/BloodMagic/issues/1083
- Fixed crash when Alchemy Table fully depleted a Cutting Fluid item
    - https://github.com/WayofTime/BloodMagic/issues/1082
- Fixed crash when scrolling through Sigil of Holding
    - https://github.com/WayofTime/BloodMagic/issues/1081
- Fixed not being able to take Gems out of Demon Crucibles
    - https://github.com/WayofTime/BloodMagic/issues/1079
- Fixed some more null stack issues
    - When is this going to be over...
    - https://github.com/WayofTime/BloodMagic/issues/1068
- Fixed JEI not displaying Armor Tomes
- Fixed Body Builder level 4 being 5x more expensive than level 5
    - https://github.com/WayofTime/BloodMagic/pull/1080
- Updated Japanese localization
    - https://github.com/WayofTime/BloodMagic/pull/1076

------------------------------------------------------
Version 2.1.5
------------------------------------------------------
- Fixed Hellfire Forge slots acting up
    - i is not j no matter how similar they look
- Fixed JEI compat for Alchemy Table recipes
- Fixed a lot more null stack checks
- Fixed (yet another) crash when creating an "invalid" stack of the Blood Tank
- Fixed Demon Will Crystal and Teleposer crashy interaction
- Fixed crash when working with toggleable sigils

------------------------------------------------------
Version 2.1.4
------------------------------------------------------
- Fixed a crash when checking if two items could be combined
- Fixed a crash when attempting to open the Sigil of Holding GUI
- Fixed a crash when somebody made an "invalid" stack of the Blood Tank
- Fixed a crash/hilarity where a Blood Altar would update itself as a Comparator
  - Thanks to Arcaratus for causing this. I haven't laughed that hard in a while.

------------------------------------------------------
Version 2.1.3
------------------------------------------------------
- Added a new Master Ritual Stone that inverts it's redstone activity
- Fixed Living Armor attributes overriding eachother instead of adding up
- Fixed Ore doubling sometimes adding invalid recipes
- Downgrade tomes are now called Downgrade Tomes
- Added some caching to rituals to (hopefully) increase performance with less world data query-ing

------------------------------------------------------
Version 2.1.2
------------------------------------------------------
- Fixed server crash when creating the guide entries

------------------------------------------------------
Version 2.1.1-70
------------------------------------------------------
- Updated to Minecraft 1.11(.2)
- Fixed the Living Armour Sacrificial Upgrade
- Fixed Bound Tools not draining LP
- Implemented the Blood Tank
  - Current recipes are temporary™
- Empty flasks can now be refilled to create a new flask
- Fixed Aura of Expulsion whitelist
- Added the Altar's Charge value to the Waila Tooltip when holding a Seer's Sigil
- Fixed meteor config error caused by EnderCore reloading configs early into startup
- Fixed meteor config not generating new entries
- Blacklisted Sentient Spectres from the Well of Suffering
- Fixed Sigils drawing from the user instead of the bound network
- Added Fluid routing to the routing nodes - this is done by using the Fluid Filter and placing a fluid container as the filter.
- Made it so the book does not give the filling recipe when the Blood Orb recipe is queried. 
- Added a null check for the Will getter for the Aura incase the WillChunk somehow does not generate. 
- [API] Added a new IMC API for modders to register their blocks as an altar component.
  - Example: Chisel Glowstone can now be used for the Tier 3 caps (once Chisel implements it)

------------------------------------------------------
Version 2.1.0-69
------------------------------------------------------
- Fixed Phantom Bridge sigil glitch when travelling between dimensions and teleporting large distances.
- Added deprecated methods for easier updating - Oops!

------------------------------------------------------
Version 2.1.0-68
------------------------------------------------------
- Fixed Ritual of Regeneration's text
- Fixed silly dupe bug with the Zephyr ritual. 

------------------------------------------------------
Version 2.1.0-67
------------------------------------------------------
- Added the Destructive Will effect to the Ritual of the Green Grove. This ritual now is done~
- Finished the Augments for the Serenade of the Nether.
- Implemented a new model for the Blood Altar to be more in-line with the rest of the mod (Thanks, wiiv!)
- Made the Blood Altar respect the new capability system for fluid management.
- Finished the Augments for the Ritual of the Feathered Knife.
- Finished the Augments for the Ritual of the Crusher.
- Finished most of the Augments for the Ritual of the Shepherd.
- Changed the Ritual of the Feathered Knife so it respects the Tough Palms Living Armour Upgrade.
- Fixed the Ritual of the Feathered Knife so that its health threshold is percent-based.
- Made the aspected Sentient Tools drop their corresponding Will type on killing enemies.
- Made it so peaceful animals provide more LP by default (to encourage creating your own farm).
- Increased the effectiveness of animals for the Gathering of the Forsaken Souls ritual by a factor of 4.
- Added the framework for the Purification Altar.
- Fixed a crash with the Ritual Diviner when attempting to replace certain blocks.
- Snares can no longer hit the thrower of the snare within 20 ticks of throwing it.
- Updated the book so it contains most of the rituals.
- Added some placeholder recipes for the Living Armour Downgrades - they will eventually change.

------------------------------------------------------
Version 2.1.0-66
------------------------------------------------------
- Made it so that when you acquire a Living Armour Upgrade from a Tome, it raises the corresponding Stat Tracker up to that upgrade level.
- Added some more framework for the Living Armour Downgrades.
- Modified the Grim Reaper's Sprint so it is better at later levels.
- Added a Repairing Living Armour Upgrade (trained by damaging the chestplate of the Living Armour while you have a full set on - it repairs all of your armour pieces over time)
- Modified the Dwarven Might skill to better change the mining speed when mining.
- Added a Dig Slowdown armour downgrade called "Weakened Pick".
- Added the framework for a ritual that grants downgrades (instead of the potion method).
- Fixed the recipes for some of the Demon Will blocks
- Added the Sigil of Elasticity, the Sigil of the Claw, and the Sigil of Winter's Breath.
- Changed most of the BlockString blocks to a BlockEnum in order to solve a loading issue with schematics.
- Added the entries for the Skeleton Turret Array and the Updraft Array
- Added the Bounce Array
- Modified the Sigil of the Phantom Bridge so it better performs with speed modifications
- Added the ability to use Mimic Blocks containing a valid Altar Component to work with the altar (#945)
  - Note: If using a Blood Rune, you do not get the ability of said rune. It just counts as a valid block.
- Fixed `/bloodmagic network get NAME` using the wrong localization (#955)
- Fixed Mimic Blocks causing an NPE with some Tile Entities (#951)
- Fixed a massive FPS drop when looking at a Master Ritual Stone while holding a Ritual Tinkerer (#950)
- Fixed an occasional crash when looking at a Mimic block with WAILA installed (#949)
- Fixed the displayed Lesser Tartaric Gem recipe in the Guide (#947)
- Fixed Dagger of Sacrifice one-shotting bosses. It now just doesn't work (#932)
- Fixed controls re-binding after each startup (#922)
- Added an alchemy array, the Array of the Fast Miner

------------------------------------------------------
Version 2.1.0-65
------------------------------------------------------
- Fixed NPE when attacking mobs when you don't have a set of Living Armour. Oops.

------------------------------------------------------
Version 2.1.0-64
------------------------------------------------------
- Fixed the symmetry issues of the Ritual of Containment
- Changed the recipe of the Acceleration rune so that it is a T4 rune.
- Added the Charging rune, which accumulates charge by using the LP from the Blood Altar (1 charge = 1 LP always). If enough charge is stored when crafting, the crafting occurs instantly.
- Added the entries for the Rune of Augmented Capacity, Charging Rune, and Rune of Acceleration
- Fixed the Sigil of Translocation so that it does not crash when picking up certain blocks. Also removed the damned lightning.
- Changed the Quick Feet upgrade and the speed boost sigil so that their speeds are applied when strafing and walking backwards as well.

------------------------------------------------------
Version 2.1.0-63
------------------------------------------------------
- Removed the darn testing code from the Divination Sigil. >.>

------------------------------------------------------
Version 2.1.0-62
------------------------------------------------------
- Fixed the fact rituals were not working (Might have been a compiler error).

------------------------------------------------------
Version 2.1.0-61
------------------------------------------------------
- Added fancy rendering for the Inversion Pillars.
- Made the Meteor registry more manageable by separating the meteors into independent .json files.
- Fixed the Sigil of Holding so that it does not crash servers when pressing the Holding key ('H' by default)

------------------------------------------------------
Version 2.1.0-60
------------------------------------------------------
- Fixed routing node serialization.
- Removed unwanted System.out lines when clicking on the slots in the routing node.

------------------------------------------------------
Version 2.1.0-59
------------------------------------------------------
- Added the Living Armour Upgrade, Nocturnal Prowess, which gives night vision in dark areas and increases damage while the area is dark.
- Added a LOT of dungeon blocks. I mean a lot.
- Finished adding the recipes for all of the blocks (except the Demon Alloy)
- Added a temporary texture (finally) for the Inspectoris Scandalum
- Fixed Specters spawning with the /give command
- Fixed the sacrifice range of the altar.
- Fixed the Regeneration ritual so that it works on non-players
- Added Mimic entry to The Architect.
- Configurified the Meteor Ritual - Modpacks and users can edit all of the major properties of the meteor ritual, including what items need to be dropped onto the ritual as well as what you get in the ritual, radius, etc. The config will refresh if there is a version change unless you set "resyncOnVersionChange" to false.
- Merged the three brick variants into one.
- Added the Inversion Pillar models and caps

------------------------------------------------------
Version 2.0.4-58
------------------------------------------------------
- Temporarily removed the dungeon stuff so that it doesn't crash.

------------------------------------------------------
Version 2.0.4-57
------------------------------------------------------
- Changed the Demon Will Aura Gauge so it refreshes based on the player's ticks existed
- Made Draft of Angelus craftable with gold dust
- Fixed Elytra upgrade
- Added the Mimics - the real ones
- Implemented a bit of framework for some T5 shenanigans.
- Fixed the Incense Altar so it properly detected the south sides of the altar.
- Updated the Filtered Item Routing Nodes' GUI so that it behaved less like ass. Also added the necessary nbt porting code.
- Further improved the routing GUI by allowing an amount to be typed into the amount bar.
- Updated the toggleable sigils so they drain the user's LP based on the user's total ticks existed instead of the world time. This is to solve the doDaylightCycle glitch in this scenario.

------------------------------------------------------
Version 2.0.4-56
------------------------------------------------------
- Fixed an incompatability between the 1.10.2 and the 1.9.4 versions

------------------------------------------------------
Version 2.0.4-55
------------------------------------------------------
- Made Guide-API a required mod.
- Fixed a funky issue where the Sound of the Cleansing Soul did not reset the upgrade points internally.
- Added Sentient Specters, which are essentially tamable companions. Drop a sentient tool if you have more than 1024 Will of a given type to summon one (at the cost of 100 Will).
- Added Mimic Blocks!

------------------------------------------------------
Version 2.0.3-54
------------------------------------------------------
- Eliminated some of the "wonkiness" from the Air Sigil
- Fixed the Hellfire Forge so that swapping Tartaric gems will not give free stuff.
- Added the Potion Flask and a few of the potion effects - max amount of effects that can be added to a single flask is currently 3.
- Fixed the Aura gauge not resetting in chunks that do not have any Aura
- Added tooltips for the progress to the next upgrade for Living Armour (hold shift + M)
- Finished off The Architect docs for now
- Finished off The Demon Kin docs for the time being

------------------------------------------------------
Version 2.0.3-53
------------------------------------------------------
- Fixed issue where the mod doesn't load on servers. Oops.

------------------------------------------------------
Version 2.0.3-52
------------------------------------------------------
- Updated the Sanguine Scientiem with Alchemy Array recipes
- Fixed the Blood Altar so that it can (again) work on a stack size larger than 1.

------------------------------------------------------
Version 2.0.3-51
------------------------------------------------------
- Added the Demon Will Aura Gauge to accurately determine the Will in the Aura.
- Added the ability for rituals to have a Demon Will set on them. Now to get rituals to use them.
- Fixed it so that the Crushing Ritual now ignores liquids
- Added the Mark of the Falling Tower ritual. 
- Changed the tooltip of the ritual diviner so that it gives information about the selected ritual.
- Added more to the Sanguine Scientiem, including Hellfire Forge recipes and Altar recipes
- Updated a lot of the text in the Sanguine Scientiem

------------------------------------------------------
Version 2.0.2-50
------------------------------------------------------
- Fixed bad NPE when using the WoS. (Well, I guess all NPEs are technically bad, but I guess it depends on your perspective. That said, I don't think a changelog is the best place for a theoretical discussion about the rights and wrongs of errors, so I will stop now.)

------------------------------------------------------
Version 2.0.2-49
------------------------------------------------------
- All Alchemy Array recipes are WIP and are subject to change! ^.^ They are created using the Arcane Ash and are shown as first item + second item.
- Added the Movement Array (feather + redstone)
- Added the Mob Beacon Array (2x Zombie Flesh)
- Added the Updraft Array (feather + glowstone)
- Added the Skeleton Turret Array (Arrow + feather)
- Fixed the Lava Crystal recipe so that it can use all tiers of blood orb
- Nerfed the Bound Blade. This is temporary, since the sword eventually will be modified.
- Fixed Teleposers so they no longer crash 1.10 instances when teleposing tiles.

------------------------------------------------------
Version 2.0.2-48
------------------------------------------------------
- Fixed a stupid bug by a stupid Way

------------------------------------------------------
Version 2.0.2-47
------------------------------------------------------
- Fixed horrible memory leak in the Living Armour.
- Item Nodes can now be disabled by a (weak) redstone signal.

------------------------------------------------------
Version 2.0.2-46
------------------------------------------------------
- Fixed it so Ritual stones can be placed by the Ritual Diviner again. Also made it so that rituals are activateable and don't crash servers.

------------------------------------------------------
Version 2.0.2-45
------------------------------------------------------
- Changed the recipe of the Sanguine Scientiem
- Fixed the Routing system so that it properly eliminates the connection to the Master node when the Master node is broken.
- Fixed an issue where the Spectral Blocks (from the Sigil of Suppression) would return the liquid on the client side before the sigil was deactivated.
- Made it so that the bound tools are (supposedly more so) unbreakable. This will probably come bite me on the ehem later, but we'll see.
- Added Fire Resistance Living Armour Upgrade, "Gift of Ignis."
- Updated The Architect section of the documentation.
- Fixed it so the Ritual will call stopRitual when a ritual stone is broken.
- Added the recipe for the Inspectoris Scandalum (I had to check if I spelled that right) which acts as a helper for Altar Building
- Right-clicking a MRS with the Ritual Diviner will affix the ritual hologram to the MRS.

------------------------------------------------------
Version 2.0.2-44
------------------------------------------------------
- Added the Draft of Angelus, which increases the max number of upgrade points for Living Armour. It's strawberry flavoured!
- Fixed the Tome of Peritia again - <= and < are the same, right?
- Fixed an interaction between the Tome of Peritia and the Experienced upgrade

------------------------------------------------------
Version 2.0.1-43
------------------------------------------------------
- Added an initial method to divine where a blood rune is missing for the next tier of altar.
- Fixed an NPE with the Gathering of the Forsaken Souls
- Fixed Absorption Hearts remaining after the absorption buff ends for the Steadfast Sentient Sword
- Updated the Guide (Woooooooooooooo........)
- (Possibly?) fixed Tome of Peritia bug of the Negative Speed of Light
- Added recipes for all ores -> dust in the alchemy table.
- Added the recipes for the Sentient Tools - yes, they all drop Will.
- Fixed the bow so that it now gives you Will when you kill a mob - it does not drop the Will, it goes directly to you.

------------------------------------------------------
Version 2.0.1-42
------------------------------------------------------
- Fixed Demon Crucible's weird behaviour where right-clicking it with an invalid item would still put the item in the crucible
	and give the player an item with a stacksize of 0.
- WAILA!
- Oh, and demon wings with the Elytra.

------------------------------------------------------
Version 2.0.1-40
------------------------------------------------------
- Bug fix version, and added several translations - some of these are still WIP.
- Fixed render bug with the Alchemy Table
- Demon WIll now drops from slimes at a reduced rate instead of not at all
- Fixed Jesus Water effect with Life Essence
- Added the Vengeful Will crystal cluster recipe. Oops! >.>
- Changed the "Default" Will into "Raw" Will. 
- Fixed an item duplication bug with how item pick-up is handled with the Zephyr ritual and the magnetism sigil.
- Made the bound Axe function... correctly
- Fixed a command block only issue with BM commands

------------------------------------------------------
Version 2.0.0-39
------------------------------------------------------
- Updated to 1.9.4!
- Fixed the Trick Shot upgrade
- Fixed Gate of the Fold endless loop
- Added rune type colors to the Ritual Diviner tooltip
- Crusher ritual ignores unbreakable blocks

------------------------------------------------------
Version 2.0.0-38
------------------------------------------------------
- Fixed it so that the Sentient weaponry is actually repairable using any demon will crystals in an anvil
- Changed the stat tracker for Tough Palms so that it counts number of hearts sacrificed, not number of sacrifices
- Fixed the Living Armour so that it wouldn't get damaged by self-sacrificing when you had upgrades that mitigate unblockable sources.
- Fixed bound sword stuttering 

------------------------------------------------------
Version 2.0.0-37
------------------------------------------------------
- Fixed Elytra upgrade not working on servers. Oops!

------------------------------------------------------
Version 2.0.0-36
------------------------------------------------------
- Added JEI compat for the Alchemy Table
- Changed the Item Routing system so that it used capabilities instead
- Updated the Alchemy Table recipe system so that it can provide better custom recipes.
- Added some more recipes (like rudimentary ore doubling) to the alchemy table.
- Added Explosive Powder, which is used to reduce cobblestone into gravel and gravel into sand (64 uses)

------------------------------------------------------
Version 2.0.0-35
------------------------------------------------------
- Changed Living Armour so that it is now damagable. The Living Armour Chestplate will be damaged, but will not break. If it gets to ~0 durability, it will damage your LP network heavily.
- Living Armour is now repairable in an anvil with Binding Reagent.
- Started adding in the Alchemy Table... not really started.
- Changed it so that the Mending enchantment consumes the EXP before the Tome of Peritia does
- Added fall distance mitigation to the jump upgrade
- Fixed Lava Crystals... again.
- Worked on the Alchemy Table
- Added the Elytra upgrade - craft the tome in an anvil by using a book and a full Elytra.

------------------------------------------------------
Version 2.0.0-34
------------------------------------------------------
- Added config to Blood Magic that will prevent the compression handler from reading the recipes added by the Compression mod (default is true).
- Added Japanese localization and fixed Czech (I can't spell) localization.

------------------------------------------------------
Version 2.0.0-33
------------------------------------------------------
- Fixed a few localization derps.
- Fixed a few minor bugs.

------------------------------------------------------
Version 2.0.0-32
------------------------------------------------------
- Changed Tome of Peritia's recipe
- Added step assist living armour upgrade tracker.
- Added new Living Armour upgrades:
	- Added Charging Strike, which increases knockback and damage for attacks while sprinting
	- Added True Strike, increasing the damage of critical hits
- Updated for Forge 12.16.0.1859
- Did some work on the Sentient Bow to start adding abilities to it
- Fixed the Speed and Jump rituals so that they correctly update the player's motion.
- Implemented the "Ritual Tinkerer" and added its recipe.

------------------------------------------------------
Version 2.0.0-31
------------------------------------------------------
- Fixed NPE when using an empty bucket.
- Added Living Armour Upgrades:
	- Strong Legs increases jump height. Pro tip: hold shift to bypass this jump boost.
	- Soft Fall decreases all fall damage, up to 100% at level 5.
	- Grave Digger, for fun times with alternative weapons
- Added increase in speed for Routing nodes inside of a chunk with Demon Aura
- Fixed OutOfBoundsException in the Sentient Sword when you didn't have enough Will.
- Fixed custom potion effects so they could be applied server-sided
- Added recipe for the Tome of Peritia

------------------------------------------------------
Version 2.0.0-30
------------------------------------------------------
- Wow, there are a lot of bug fixes lately.
- Fixed it so that the blood orb is no longer consumed in the crafting grid.

------------------------------------------------------
Version 2.0.0-29
------------------------------------------------------
- Fixed formatting not working properly on servers.

------------------------------------------------------
Version 2.0.0-28
------------------------------------------------------
- Fixed the soul snare.
- Removed health buff from Steadfast armour
- Changed corrosive sword's effect from poison to wither
- Fixed living armour's training bracelet so that it deactivates any training of upgrades not supported
- Added "Experienced" living armour upgrade, which provides more xp from picked up orbs.
- Updated to Forge 1826

------------------------------------------------------
Version 2.0.0-27
------------------------------------------------------
- Added OreDict support for the altar (was done in 26)
- Made it so that the Tartaric gem would show visually what type of will was contained
- Allowed the Sentient Sword to use different wills, and change its colour based on the used one. Also made it so you do not toggle it by right clicking, but it simply 
	rechecks itself when you smack something and when you right-click.
- Fixed item binding. Yusssss.
- Added Sword, Armour, and Bow texture changes when you have different demonic will in your inventory.
- Finalized sentient sword effects
- Did work on the unique demon will effects for armour
- FINALLY changed it so farm animals do not drop demon will.

------------------------------------------------------
Version 2.0.0-23
------------------------------------------------------
- Fixed "see through world" syndrome for most blocks
- Fixed .obj models so that they will properly render while in-hand
- Fixed routing node attaching logic
- Changed the growth behavior of the crystals
- Fixed Potion getting for various methods
- Started work on crystal automation ritual
- Finished first iteration of the iterator of AreaDescriptor (hehe)
- Finished the crystal automation ritual, "Gathering of the Forsaken Souls." This ritual damages mobs in it's area (21^3 cube around the MRS) and once it kills a mob it
	uses its life essence to feed the demonic will crystals inside of its area (two blocks above the ritual stone, 7x7 region and 5 high). This costs LP and 
	demonic will from the aura of the particular type, and costs less will the more... varied the mobs killed are. Growth amount is dictated by the max health
	of the mobs killed.
- Updated to Forge 1811

------------------------------------------------------
Version 2.0.0-22
------------------------------------------------------
- Fixed client disconnect when joining servers
- Fixed Bound Blade having same damage between activated and deactivated states
- Fixed Sentient Armour gem so it works, now
- Fixed Armour model textures
- Fixed crash on newer versions of Forge
- Updated zh_CN localization

------------------------------------------------------
Version 2.0.0-21
------------------------------------------------------
- Initial release to 1.9. Many glitches that have yet to be ironed out, some of them Forge issues. Probably best not to play with this one until Forge stabilizes.

------------------------------------------------------
Version 2.0.0-20
------------------------------------------------------
- Fixed Blood Altar crashing on odd occasions.
- Fixed GUI of hellfire forge.
- Fixed issue with Will Chunks not generating when requested in new chunks
- Fixed issue where Orb Tier was never set for a player internally
- Fixed Creative Dagger. It now works correctly. Shift + Right Click on an Altar to fill it to capacity.
- Fixed step height being set to incorrect value. Your Chisels and Bits stairs should work, now!
- Fixed Bound Tools not being enchantable.
- Fixed Bound Tools "Beast Mode" breaking blocks unreliably.
- Fixed Blood Light Sigil not using LP
- Changed Will system so that it automatically goes into your inventory and then drops any excess Will on the ground.
- Changed Blood Light to render more particles when the sigil is held to make them easier to find.
- Added debug commands. These work exactly the same as in the 1.7.10 version
- [API] Fixed AddToNetworkEvent not being cancellable even though it should be
- [API] Helper method in NetworkHelper to obtain the highest possible capacity for a given tier.

------------------------------------------------------
Version 2.0.0-19
------------------------------------------------------
- Fixed path blocks so they are actually craftable.
- Added gui stuff to enable priority in the item routing system: nodes with a lower value priority will be accessed first. (May be rotated in the future)
- Grayed out the currently active side's button in the item routers.
- Added Demon Pylon
- Changed behaviour of Demon Crucible
- Fully implemented the behaviour of the crystal growing process.
	- A Demon Crucible will be able to syphon the demon will from the tartaric gems inside of its inventory - right click with a gem to place it into the inventory.
	- The syphoned will from the gem will go into the chunk, staying in the air - it will not move to neighbouring chunks unless forced to do so through other means
	- The Demon Pylon draws will in the air from surrounding chunks and puts it into its own chunk - this acts as sort of like a pressure system, where it will even out the will distribution, but only goes one way.
	- The Demon Crystallizer takes 100 will from the air (need a demon crucible for it to work) and forms a demon crystal on top of it. There is a 10% chance that this crystal will be of a special type.
	- The Demon Crystals can be mined in order to collect their crystals. These crystals can be used for crafting more powerful stuff. If you have over 1024 will on your person when you right click the crystal, you can harvest a single crystal instead of the entire thing.
	- You can put the harvested crystals inside of a demon crucible. Each crystal is worth 10 will, so this acts as a way to semi-automate will creation.
	- When a hellfire forge is inside of a chunk with demon will, it will fill its contained tartaric gem with will.
	- Changed a few recipes (like the greater tartaric gem) to match this new system.

------------------------------------------------------
Version 2.0.0-18
------------------------------------------------------
- Fixed issue with lava crystal working even their is no LP available.
- Added upgrade trainer
- Fixed step height issue with C&B stuff. Apparently step height is now 0.6 by default instead of 0.5.
- Added Ritual: Song of the Cleansing Soul to remove Living Armour upgrades from the armour.
- Added Ritual: Ritual of Living Evolution, which is used to set the living armour's max upgrade points to 300.
- Added Ritual: The Timberman to cut down trees. Requires a chest on top of the MRS in order to collect the drops from harvested trees.
- Added Ritual: Hymn of Syphoning, which syphons up fluids around it in a 16 block radius and puts the fluid in the tank above it. The tank above it must have at least one bucket of the fluid you want to syphon inside of it.
- Added Ritual: The Assembly of the High Altar, which places runes and blocks from the inventory above it in the world in the form of a blood altar. Autobuilding altars~
- Added Ritual: The Gate of the Fold. 
- Added Ritual: The Filler. Places blocks from the inventory on top of it in the world.
- Added Ritual: Le Vulcanos Frigius

- Added tooltip to living armour to show the current upgrade points of the armour.
- Added recipe for the training bracelet. Combine in an anvil with an upgrade tome to set it as the upgrade to train.
- Ammended range of Zephyr ritual
- Fixed Green Grove ritual
- Fixed Crusher ritual so it didn't break everything at once.
- Removed the FOV effect from the Quick Feet speed upgrade.
- Minor work on the Demon Crucible.
- Crucibles now fill the tartaric gems of close-by hellfire forges
- Fixed rituals not correctly re-activating when taking off a redstone signal after reloading the world.
- Added Teleposition Sigil, which teleports the user to the bound Teleposer.
- Added Transposition Sigil, which picks up the block clicked on including the NBT of the given tile.

------------------------------------------------------
Version 2.0.0-17
------------------------------------------------------
- Added Living Armour Upgrades
	- Solar Powered
	- Grim Reaper's Sprint
	- [Thaumcraft] Runic Shielding
- Fixed Blood Altar's progress resetting when clicking with another item
- Fixed Divination and Seer sigils crashing when clicking on an altar while not bound
- Added crafting recipes for the following sigils:
	- Compression
	- Phantom Bridge
	- Ender Severance
	- Haste
- Dagger now bypasses checks of armour

------------------------------------------------------
Version 2.0.0-16
------------------------------------------------------
- Fixed a major bug with the Blood Orbs.

------------------------------------------------------
Version 2.0.0-15
------------------------------------------------------
- Added blood lamp sigil array texture and reagent. Made it so the blood lamp sigil will place the light when right clicking on a block.
- Added magnetism sigil array texture and reagent.
- Added the ability for the blood altar to have its buffer tanks accessed (Semi-tested)
- Improved readability for large numbers
- Fixed break particle for Alchemy Array
- Waila handling for Alchemy Array
- Fixed Ritual eating LP when reactivated via a Redstone signal
- Ritual Stones now always drop blank stones
- Bound Blade is now enchant-able
- Goggles of Revealing upgrade for Living Helmet. Combine a Living Helmet and Goggles of Revealing in an Anvil
- Fixed config for disabling rituals
- [API] Lots of changes to the binding system. It's now handled automatically for all items that implement IBindable.
- Added Incense Altar system.
- Added models for the routing nodes, incense altar, and the hellfire forge.

------------------------------------------------------
Version 2.0.0-14
------------------------------------------------------
- [API] Added support for some IMC methods:
    - FMLInterModComs.sendMessage("BloodMagic", "teleposerBlacklist", ItemStack)
    - FMLInterModComs.sendMessage("BloodMagic", "sacrificeValue", "ClassName;Value")
    - FMLInterModComs.sendMessage("BloodMagic", "greenGroveBlacklist", "domain:name")
- Fixed the bound sword so that it now drops weak blood shards.
- Fixed the demon will so that you can actually pick it up!

------------------------------------------------------
Version 2.0.0-13
------------------------------------------------------
- Added recipes and temp textures for path blocks. More path blocks to come.
- Tweaked the Incense altar and added its recipe.
- Fixed Blood Light sigil not providing the right coloured particles
- Added the ability for the divination sigil to look up information about the incense altar.
- Fix another NPE when checking of a player has a full set of Living Armor

------------------------------------------------------
Version 2.0.0-12
------------------------------------------------------
- This build doesn't exist. Shhh

------------------------------------------------------
Version 2.0.0-11
------------------------------------------------------
- Fix NPE when checking if a player has a full Living Armor set

------------------------------------------------------
Version 2.0.0-10
------------------------------------------------------
- Added recipe for Sentient Armour - craft a Sentient Armour Gem and right click with it to toggle the armour.
- Added recipes for the Greater and Grand Tartaric gems. These are temporary recipes.
- Updated textures.
- Added ability to fill gems from other gems. Right click with a gem to fill other gems in your inventory.
- Un-nerfed the Ritual Stone recipe to provide 4 again
- Rituals can now be toggled with Redstone. Provide a redstone signal to the Master Ritual Stone to turn it off.
- Made the Sentient sword able to block. 
- Fixed the Sentient Sword so that even when not activated it will still do damage - just at a reduced amount.
- Made it so that Demon Will will be voided if all of the player's tartaric gems are filled.

------------------------------------------------------
Version 2.0.0-9
------------------------------------------------------
- Fixed the rituals so that they no longer caused nausea no matter what.

------------------------------------------------------
Version 2.0.0-8
------------------------------------------------------
- Fixed crash when activating rituals while operating on servers
- Added the majority of stuff for the item routing system.

------------------------------------------------------
Version 2.0.0-7
------------------------------------------------------
- JEI now displays more information for Altar recipes. Hover over the Altar image to view it.
- Added particles to the Blood Altar on the server-side.
- Allow configuration of entity sacrificial values
- [API] Allow setting of entity sacrificial values via API. Takes precedence over config values.
- [API] Method to easily get instances of Items and Blocks
- [API] Allow mods to blacklist their blocks from the Green Grove ritual/sigil

------------------------------------------------------
Version 2.0.0-6
------------------------------------------------------
- Fixed the LivingArmourTracker for defense so that it no longer ate up armour (om nom nom nom)

------------------------------------------------------
Version 2.0.0-5
------------------------------------------------------
- Tweaked Sentient Sword's will drop rate
- No longer 1.8.8 compatible
- Cleaned some clutter from JEI
- Added a potential fix to some server mod issues.


------------------------------------------------------
Version 2.0.0-4
------------------------------------------------------
- Added Physical resistance upgrade (Tough skin)
- Added health boost upgrade (Healthy)
- Added melee damage upgrade (Fierce strike)
- Added trick shot upgrade (Have fun finding it! :D)
- Added T5 orb recipe and Demonic Activation Crystal
- Added Rituals:
	- Animal Growth ritual
	- Harvest ritual
	- Magnetism ritual
	- Crushing ritual
	- Full Stomach ritual
	
- Added parts for the new Demon Will System
	- Added the Demon Will item
	- Added the Sentient Sword, which will be powered by consuming demon will
	- Added a Lesser Tartaric Gem in order to hold onto more will
	- SSSSSSSSoooooooouuuuuuulllllllllsssssss
	- Added Hellfire Forge block
	- Added rudimentary snare and recipe - has 25% chance for monster to drop its soul
	- Added Sentient Bow
	- Added Sentient Armour
	
- Fixed binding of togglable sigils
- Added saving for ritual tags

------------------------------------------------------
Version 2.0.0-3
------------------------------------------------------
- Fixed client-side issue when shift-clicking lava crystals into a furnace while on a server.
- Added poison upgrade to Living Armour
- Fixed digging upgrade
- Added self sacrifice upgrade to Living Armour (Tough Palms)
- Added knockback resistance upgrade to Living Armour, trained via eating (Body Builder)


------------------------------------------------------
Version 2.0.0-2
------------------------------------------------------
- Added Life Essence bucket recipe
- Added the rendering of LP inside of the altar
- Added Living Armour including some upgrades:
	- Added speed upgrade
	- Added digging upgrade
- Modified Divination Sigil recipe - no longer uses a block of glass because of issues on servers (tempramental)
- Modified Apprentice Blood Orb recipe to require a block of redstone instead.
- Added the Reagent Air recipe.


------------------------------------------------------
Version 2.0.0
------------------------------------------------------

Initial release of the open beta for the mod to Minecraft v1.8.9! This mod version has a lot of changes. Because it is a full-on rewrite, some of the mechanics of the mod have changed/are going to change. As such, the feel of the mod will be slightly different than previous iterations.
This mod has a large amount of compatibility with the recipe look-up mod, Just Enough Items (JEI). It is VERY much recommended that you download JEI, since it will make your life a lot easier with the new mechanics.
Because this is a beta of a rewrite, there will be a lot of missing content. I am trying my hardest to add new content as fast as I can, but my team and I are only human and can only code so fast. Please give a little patience while we make the mod the best it possibly can!
Also, please submit bug reports or feature requests to the github, https://github.com/WayofTime/BloodMagic. We'll get to the issue soon!

New
- Added "Alchemy Array" crafting - this is achieved by making some "Arcane Ash" and clicking on the ground to create an array. You then click on the array with the first ingredient and then the second - breaking the array will give the items back.
- Added JEI compatibility
- Added WAILA compatibility (No more needing 3rd-party addons!)

Changed
- Lots of internals have changed. Too many to list.
- A graphical overhaul of everything has been implemented. These textures were provided by CyanideX's Unity texture pack. BBoldt and Yulife (Inap) from the Unity team are our new permanent texture artists.

Bug Fixes
-
