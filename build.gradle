plugins {
    id 'eclipse'
    id 'idea'
    id 'maven-publish'
    id 'net.minecraftforge.gradle' version '[6.0,6.2)'
    id 'org.spongepowered.mixin' version '0.7.+'
}

version = minecraft_version + '-' + mod_version
group = 'wayoftime.bloodmagic' // http://maven.apache.org/guides/mini/guide-naming-conventions.html

base {
    archivesName = mod_id
}

java.toolchain.languageVersion = JavaLanguageVersion.of(17)

println('Java: ' + System.getProperty('java.version') + ' JVM: ' + System.getProperty('java.vm.version') + '(' + System.getProperty('java.vendor') + ') Arch: ' + System.getProperty('os.arch'))
minecraft {
    mappings channel: mapping_channel, version: mapping_version

    accessTransformer = file('src/main/resources/META-INF/accesstransformer.cfg')
    copyIdeResources = true

    runs {

        configureEach {
            workingDirectory project.file('run')

//            properties 'mixin.env.disableRefMap': 'true'

            // Recommended logging data for a userdev environment
            property 'forge.logging.markers', 'SCAN,REGISTRIES,REGISTRYDUMP'

            // Recommended logging level for the console
            property 'forge.logging.console.level', 'debug'

            property 'mixin.env.remapRefMap', 'true'
            property 'mixin.env.refMapRemappingFile', "${projectDir}/build/createSrgToMcp/output.srg"

            mods {
                "${mod_id}" {
                    source sourceSets.main
                }
            }

        }
        client {
            property 'forge.enabledGameTestNamespaces', mod_id
        }

        server {
            property 'forge.enabledGameTestNamespaces', mod_id
            args '--nogui'
        }

        gameTestServer {
            property 'forge.enabledGameTestNamespaces', mod_id
        }

        data {
            workingDirectory project.file('run-data')
            args '--mod', mod_id, '--all', '--output', file('src/generated/resources/'), '--existing', file('src/main/resources/')
        }
    }
}
// Include resources generated by data generators.
sourceSets.main.resources { srcDir 'src/generated/resources' }
mixin {
	add sourceSets.main, "bloodmagic.refmap.json"
	
	config "bloodmagic.mixins.json"
}

repositories {
    maven {
        name = "The Loader"
        url = "https://maven.blamejared.com"
    }
    maven {
        // Curios
        url = "https://maven.theillusivec4.top/"
    }
    maven {
        // Mystical Agriculture, Cucumber (library)
        url = "https://maven.blakesmods.com/"
    }
}

dependencies {
    minecraft "net.minecraftforge:forge:${minecraft_version}-${forge_version}"

    annotationProcessor "org.spongepowered:mixin:${mixin_version}:processor"
    implementation(annotationProcessor("io.github.llamalad7:mixinextras-common:${mixin_extras}"))
    implementation(jarJar("io.github.llamalad7:mixinextras-forge:${mixin_extras}")) {
        jarJar.ranged(it, "[${mixin_extras},)")
    }

    compileOnly fg.deobf("mezz.jei:jei-${minecraft_version}-common-api:${jei_version}")
    compileOnly fg.deobf("mezz.jei:jei-${minecraft_version}-forge-api:${jei_version}")
    runtimeOnly fg.deobf("mezz.jei:jei-${minecraft_version}-forge:${jei_version}")

    compileOnly fg.deobf("vazkii.patchouli:Patchouli:${patchouli_version}:api")
    runtimeOnly fg.deobf("vazkii.patchouli:Patchouli:${patchouli_version}")

//    compileOnly fg.deobf("com.blamejared.crafttweaker:CraftTweaker-1.18.2:9.1.154")
    
    runtimeOnly fg.deobf("top.theillusivec4.curios:curios-forge:${curios_version}")
    compileOnly fg.deobf("top.theillusivec4.curios:curios-forge:${curios_version}:api")

    compileOnly fg.deobf("com.blakebr0.mysticalagriculture:MysticalAgriculture:${minecraft_version}-${mysticalag_version}:api")
    // enable below to get MAG enabled in the runs
    //runtimeOnly fg.deobf("com.blakebr0.mysticalagriculture:MysticalAgriculture:${minecraft_version}-${mysticalag_version}")
    //runtimeOnly fg.deobf("com.blakebr0.cucumber:Cucumber:${minecraft_version}-${cucumber_version}")
}

tasks.named('processResources', ProcessResources).configure {
    var replaceProperties = [
            minecraft_version: minecraft_version, minecraft_version_range: minecraft_version_range,
            forge_version: forge_version, forge_version_range: forge_version_range,
            loader_version_range: loader_version_range,
            mod_id: mod_id, mod_name: mod_name, mod_license: mod_license, mod_version: mod_version,
            mod_authors: mod_authors, mod_description: mod_description,
    ]
    inputs.properties replaceProperties

    filesMatching(['META-INF/mods.toml', 'pack.mcmeta']) {
        expand replaceProperties + [project: project]
    }
}

tasks.named('jar', Jar).configure {
    manifest {
        attributes([
            "Specification-Title": mod_id,
            "Specification-Vendor": mod_authors,
            "Specification-Version": "1", // We are version 1 of ourselves
            "Implementation-Title": project.name,
            "Implementation-Version": project.jar.archiveVersion,
            "Implementation-Vendor" : mod_authors,
            "Implementation-Timestamp": new Date().format("yyyy-MM-dd'T'HH:mm:ssZ")
        ])
    }
    finalizedBy 'reobfJar'
}



task srcJar(type: Jar) {
    from(sourceSets.main.java)
    archiveClassifier = 'sources'
}

task apiJar(type: Jar) {
    from(sourceSets.main.allJava)
    from(sourceSets.main.output)
    include 'wayoftime/bloodmagic/api/**'

    archiveClassifier = 'api'
}

artifacts {
    archives srcJar, apiJar
}

publishing {
    publications {
        register('mavenJava', MavenPublication) {
            artifact jar
            artifact srcJar
            artifact apiJar
        }
    }
    repositories {
        maven {
            url "file:///${project.projectDir}/mcmodsrepo"
        }
    }
}
tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8' // Use the UTF-8 charset for Java compilation
}