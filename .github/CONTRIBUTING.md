# Contributing to BloodMagic

First off, thank you for taking the time to contribute!

## Issues

First, make sure the issue has not been already reported. 

If it has not been reported, follow the provided issue template (created when you click "New Issue").

If it has been reported, provide any additional information you can to the current active issue.

## Pull Requests

With the 1.8 rewrite comes a need to keep the code *clean*. Pull Requests will be looked over a bit stricter from now on.

When you wish to contribute, please keep these points in mind:

###Do:
* Only make 1 commit for each major change. 
    * This helps avoid "polluting" the Git history.
* Squash extra commits.
    * See above.
* Describe each and every change you make in your Pull Request.
    * This lets everybody know exactly what is going on for easy discussion.
* Make short yet descriptive commit titles.
    * Feel free to give a very basic overview of the commit in the message, then use the description to go into detail.
* Keep your formatting the same as the project. 
    * This will be expanded upon at a later date.

###Do not:
* Make unnecessary changes to files.
    * If you don't need to touch it, don't touch it. 
    * This includes: *renaming args*, *renaming files*, *editing formatting*, etc.
* Use obfuscated names for parameters. 
    * This makes the code look messier and less "professional".