{"type": "forge:conditional", "recipes": [{"conditions": [{"type": "forge:mod_loaded", "modid": "mysticalagriculture"}], "recipe": {"type": "bloodmagic:meteor", "input": {"item": "mysticalagriculture:prosperity_shard"}, "syphon": 500000, "explosion": 24.0, "layers": [{"radius": 7, "additionalWeight": 200, "minWeight": 0, "weightMap": [{"tag": "#forge:ores/prosperity#0", "weight": 100}, {"tag": "#forge:ores/inferium#0", "weight": 100}], "fill": "minecraft:stone"}, {"radius": 3, "additionalWeight": 0, "minWeight": 0, "weightMap": [{"tag": "#forge:ores/soulium#0", "weight": 100}, {"tag": "minecraft:soul_sand", "weight": 50}, {"tag": "minecraft:soul_soil", "weight": 50}], "fill": "minecraft:stone"}]}}]}