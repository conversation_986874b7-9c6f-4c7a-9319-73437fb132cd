{"type": "forge:conditional", "recipes": [{"conditions": [{"type": "forge:mod_loaded", "modid": "appliedenergistics2"}], "recipe": {"type": "bloodmagic:meteor", "input": {"tag": "forge:gems/certus_quartz"}, "syphon": 500000, "explosion": 24.0, "layers": [{"radius": 2, "additionalWeight": 0, "minWeight": 0, "weightMap": [{"tag": "appliedenergistics2:fluix_block", "weight": 100}], "fill": "appliedenergistics2:sky_stone_block"}, {"radius": 7, "additionalWeight": 200, "minWeight": 0, "weightMap": [{"tag": "#forge:ores/certus_quartz#0", "weight": 100}, {"tag": "#forge:storage_blocks/quartz#0", "weight": 50}], "fill": "appliedenergistics2:sky_stone_block", "shell": "appliedenergistics2:sky_stone_block"}]}}]}