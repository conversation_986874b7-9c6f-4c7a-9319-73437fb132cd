{"name": "Sigil of Holding", "icon": "bloodmagic:sigilof<PERSON>", "category": "bloodmagic:alchemy_array/sigil", "pages": [{"type": "patchouli:text", "text": "This Sigil can hold up to 5 other $(item)Sigils$() at a time, providing you with their passive effects and allowing you to activate them on a whim.$(br)Press [$(k:bloodmagic.keybind.open_holding)] while holding the Sigil to open its inventory.$() $(br)Press [$(k:bloodmagic.keybind.cycle_holding_pos)] to cycle forward,$() or [$(k:bloodmagic.keybind.cycle_holding_neg)] to cycle backwards.$() holding [$(k:sneak)] and using your mousewheel also works."}, {"type": "bloodmagic:crafting_2-step_sigil", "alchemy_table.heading": "Holding Reagent", "alchemy_table.recipe": "bloodmagic:alchemytable/reagent_holding", "array.heading": "Sigil of Holding", "array.recipe": "bloodmagic:array/holdingsigil", "array.text": "$(italic)Sigil-ception"}, {"type": "patchouli:text", "flag": "mod:curios", "text": "As you have the $(thing)Curios API$() installed, you can equip this $(item)Sigil$() as a charm. If you want to wear more curios at once, consider using the $(l:bloodmagic:alchemy_array/living_equipment/armor_upgrades/curios_sockets)Socketed Upgrade$() for your $(l:bloodmagic:alchemy_array/living_equipment/living_basics)Living Armour$()."}]}