{"name": "Seer's <PERSON><PERSON><PERSON>", "icon": "bloodmagic:see<PERSON><PERSON>l", "category": "bloodmagic:alchemy_array/sigil", "pages": [{"type": "patchouli:text", "text": "The $(item)Seer's Sigil$() is a more advanced form of the $(l:bloodmagic:alchemy_array/sigil/divination)Divination Sigil$().  Alongside showing the amount of LP in the bound player's $(l:bloodmagic:altar/soul_network)Soul Network$(/l), it also shows more information when looking at a $(l:bloodmagic:altar/blood_altar)Blood Altar$(/l). $(br2)Like the $(item)Divination Sigil$(), it can also be used to edit your GUI. (See the Divination Sigil's entry for more info)"}, {"type": "bloodmagic:crafting_2-step_sigil", "alchemy_table.heading": "Sight Reagent", "alchemy_table.recipe": "bloodmagic:alchemytable/reagent_sight", "array.heading": "Seer's <PERSON><PERSON><PERSON>", "array.recipe": "bloodmagic:array/seersigil", "array.text": "$(italic)When seeing all is not enough"}, {"type": "patchouli:text", "text": "From top to bottom, we have: $(li)The current Tier of the $(l:bloodmagic:altar/blood_altar)Blood Altar$(/l). $(li)The amount of blood currently inside the Altar, and the current total capacity of the Altar. (This defaults to 10,000mb, but may be increased with $(l:bloodmagic:altar/blood_rune/capacity_rune)Runes of Capacity$() and $(l:bloodmagic:altar/blood_rune/aug_capacity_rune)Runes of Augmented Capacity.$() $(li)The current crafting progress, if any. $(li)LP Consumption/Tick - how much LP the Altar will use per tick when crafting. $(li) Current LP Storage of any $(l:bloodmagic:altar/blood_rune/charging_rune)Charging Runes$() you may have."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/sigil/seer_sigil_info.png"], "title": "<PERSON><PERSON>'s <PERSON><PERSON><PERSON>", "border": true, "text": "The Seer's <PERSON><PERSON><PERSON> displays this HUD when you are looking at a Blood Altar."}]}