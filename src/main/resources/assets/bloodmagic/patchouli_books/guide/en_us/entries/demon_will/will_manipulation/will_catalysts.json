{"name": "Will Catalysts", "icon": "bloodmagic:rawcatalyst", "category": "bloodmagic:demon_will/will_manipulation", "pages": [{"type": "patchouli:text", "text": "if $(raw)Demon Will$() has one drawback, it's that collecting it is a slow, tedious process. Even with an almost full $(l:bloodmagic:demon_will/will_manipulation/soul_gem)Tartaric Gem$() and a $(l:bloodmagic:demon_will/demonic_items/sentient_sword)Sentient Sword$() enchanted with $(thing)Looting III$() and further buffed with $(l:bloodmagic:alchemy_table/anointments/looting_anointment)Plunderer's Glint II$(), it's still a manual process, and you have better things to do with your genius than scramble about splatting spiders and slaying skeletons."}, {"type": "patchouli:text", "text": "Fortunately, it's possible to completely automate this procedure, leaving you with more time on your hands to expand your evil empire. (Or go back to farming beets, if you prefer.) $(br2)The first step is to get some $(l:bloodmagic/demon_will/will_manipulation/crystallized_will)Will Crystals$(). We can use any kind - $(raw)Raw$(),  $(steadfast)Steadfast$(), $(destructive)Destructive$(), $(vengeful)Vengeful$() or $(corrosive)Corrosive$() Will, as long as we have 4 of the same kind of crystal."}, {"type": "bloodmagic:3x_crafting_soulforge", "a.heading": "Raw Crystal Cluster", "a.recipe": "bloodmagic:soulforge/raw_crystal_block", "b.heading": "Steadfast Cluster", "b.recipe": "bloodmagic:soulforge/steadfast_crystal_block", "c.heading": "Destructive Cluster", "c.recipe": "bloodmagic:soulforge/destructive_crystal_block"}, {"type": "bloodmagic:2x_crafting_soulforge", "a.heading": "Vengeful Cluster", "a.recipe": "bloodmagic:soulforge/vengeful_crystal_block", "b.heading": "Corrosive Cluster", "b.recipe": "bloodmagic:soulforge/corrosive_crystal_block"}, {"type": "patchouli:text", "text": "once you have this cluster, simply place it down in any chunk, supply the chunk with will of the matching type, and wait. Eventually, new spires will grow, just like clusters growing atop a $(l:bloodmagic:demon_will/will_manipulation/crystallized_will)Demon Crystallizer$(). $(br2)You can even automate the breaking of these additional spires with the $(l:bloodmagic:rituals/ritual_list/ritual_crystal_harvest)Crack of the fractured Crystal$() ritual, and the collection of the resulting crystals with the $(l:bloodmagic:rituals/ritual_list/ritual_zephyr)Call of the Zephyr$()."}, {"type": "patchouli:text", "text": "With a basic $(l:bloodmagic:demon_will/item_routing/routing_nodes)Routing Node system$(), you can even feed these excess crystals back into a $(l:bloodmagic:demon_will/will_manipulation/crystallized_will)Demon Crucible$() for a totally automatic, net-positive loop. $(br2)You may have noticed a bit of a problem, however - this setup is slow. Very, very slow. Each crystal only sprouts a new spire once every few minutes, and takes almost as much will to spawn as you gain from burning it - on average, it comes out to about 1 will / minute / spire."}, {"type": "bloodmagic:3x_crafting_soulforge", "a.heading": "Raw Will Catalyst", "a.recipe": "bloodmagic:soulforge/raw_catalyst", "b.heading": "Steadfast Will Catalyst", "b.recipe": "bloodmagic:soulforge/steadfast_catalyst", "c.heading": "Destructive Will Catalyst", "c.recipe": "bloodmagic:soulforge/destructive_catalyst"}, {"type": "bloodmagic:2x_crafting_soulforge", "a.heading": "Vengeful Will Catalyst", "a.recipe": "bloodmagic:soulforge/vengeful_catalyst", "b.heading": "Corrosive Will Catalyst", "b.recipe": "bloodmagic:soulforge/corrosive_catalyst"}, {"type": "patchouli:text", "text": "Fortunately, this is where our new friends, $(item)Will Catalysts$() step in. While holding one of these Catalysts, simply press [$(k:use)] on a $(thing)Crystal Cluster$() of the same type to turbocharge its growth! Each catalyst reduces the amount of will required to grow a spire from 45 to just 25, and it speeds the growth up tenfold. Every dose is good for ten spires worth of growth, which makes for a net bonus of 200 will per Catalyst."}, {"type": "patchouli:text", "text": "You can even double-dose your clusters - though this only makes the effect last for 20 growths instead of 10 and has no other benefit. $(br2)Of course, the canny Sanguimancer will have realised that this has replaced one manual problem - running around and bopping monsters with a sword - with another one - running around and bopping crystals with a catalyst."}, {"type": "patchouli:text", "text": "Fortunately, there's a ritual for that too! the $(l:bloodmagic:rituals/ritual_list/ritual_crystal_catalyst)Gathering of the Forsaken Souls$() will automatically apply catalysts to any crystals in its area of effect. With a few farms and a very, very clever $(thing)Routing Node$() setup, you can automate the whole thing, top to bottom... But as they say, that's left as an exercise for the reader."}]}