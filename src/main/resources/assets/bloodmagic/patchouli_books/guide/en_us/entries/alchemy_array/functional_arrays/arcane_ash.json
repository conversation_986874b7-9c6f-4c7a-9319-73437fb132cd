{"name": "Alchemy Array Basics", "icon": "bloodmagic:arcaneashes", "category": "bloodmagic:alchemy_array/functional_array", "pages": [{"type": "patchouli:text", "text": "$(item)Arcane Ashes$() are an item that is pivotal in the creation of Alchemy Arrays. $(item)Arcane Ashes$() can be crafted in the $(l:bloodmagic:alchemy_table/alchemy_table)Alchemy Table$() using some early game items."}, {"type": "bloodmagic:crafting_alchemy_table", "heading": "Arcane Ashes", "recipe": "bloodmagic:alchemytable/arcane_ash"}, {"type": "patchouli:text", "text": "In order to create an Alchemy Array, press [$(k:use)] while looking at a block with the $(item)Arcane Ashes$() in hand. This will consume 1 durability out of 20 from the $(item)Arcane Ashes$() and draw a simple $(thing)Alchemy Array$(), that by itself has no effects. $(br2)When you click on the $(thing)Alchemy Array$(), it will consume a single $(item)item$() from the stack in your hand and hold it in the array. These items are then used to determine the $(thing)Alchemy Array$()'s effect."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/alchemy_array/simple_array.png", "bloodmagic:images/entries/alchemy_array/divination_array_1.png", "bloodmagic:images/entries/alchemy_array/divination_array_2.png"], "title": "Alchemy Array", "border": true, "text": "$(thing)Alchemy Array$() showing the array with: no inputs; only the $(item)base item$(); both $(item)base$() and $(item)catalyst$()."}, {"type": "patchouli:text", "text": "Each effect requires two items: a $(item)base$() and a $(item)catalyst$(). The $(item)base$() is the first item that you click the array with after it is drawn, and the $(item)catalyst$() is the second item. When you apply the base item, the design of the array will change if it is valid, and the array will activate once you apply the catalyst."}, {"type": "patchouli:text", "text": "Although only a few arrays are currently implemented, eventually you will have arrays that range from simple $(l:bloodmagic:alchemy_array/functional_arrays/crafting_array)crafting arrays$(/l) to even teleportation arrays."}]}