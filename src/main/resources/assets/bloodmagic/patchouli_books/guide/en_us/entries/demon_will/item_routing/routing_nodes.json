{"name": "Routing <PERSON><PERSON>", "icon": "bloodmagic:masterrouting<PERSON>de", "category": "bloodmagic:demon_will/item_routing", "priority": "true", "pages": [{"type": "patchouli:text", "text": "Hauling items around by hand may be all right for some people, but we are a Sanguimancer. What's more, we have access to $(raw)Demon Will$()! Surely there's a better way to go about things. $(br2)As such, you've managed to come up with $(thing)Routing Nodes$(). These offer a powerful way to transport, sort, and filter items, sending them magically through the air as you decree."}, {"type": "patchouli:text", "text": "$(thing)Routing Node Networks$() have 3 main components: $(item)Input Nodes$(), $(item)Output Nodes$(), and the $(item)Master Routing Node$(). $(br2)Every network requires exactly 1 $(item)Master Routing Node$(), and every other Node in the network must be able to trace a route back to the Master, whether directly, or via other Nodes. $(br2)Input and Output nodes can withdraw or insert items from any block with an accessible inventory, respectively."}, {"type": "bloodmagic:crafting_soulforge", "heading": "Master Routing <PERSON>de", "recipe": "bloodmagic:soulforge/master_routing_node", "text": "The Master Node will be able to accept upgrades in the future, but for now it only serves to control and direct the network, acting as its 'Brain'."}, {"type": "patchouli:text", "text": "A $(item)Master Routing Node$() is all well and good, but without the other two node types, it's not particularly useful on its own, so let's get on to those next. $(br2)$(item)Input Routing Nodes$() draw items in to the network, $(item)Output Routing Nodes$() export them out again, and plain old $(item)Routing Nodes$() serve to extend the reach of your network. As Input and Output nodes are currently otherwise identical, we will be focusing on the Input Routing Node unless otherwise specified."}, {"type": "bloodmagic:crafting_soulforge", "heading": "Routing <PERSON><PERSON>", "recipe": "bloodmagic:soulforge/routing_node", "text": "Doesn't do a whole lot on its own, but can be used to extend $(thing)Routing Networks$() beyond the 16-block reach of a single connection."}, {"type": "bloodmagic:2x_crafting_soulforge", "a.heading": "Input Routing Node", "a.recipe": "bloodmagic:soulforge/input_routing_node", "b.heading": "Output Routing Node", "b.recipe": "bloodmagic:soulforge/output_routing_node"}, {"type": "patchouli:text", "text": "When you place an Input or Output Node down, it will automatically connect to $(thing)all adjacent inventories$(), but it won't do anything without a $(item)Filter$() of some kind in at least one of its side's slots. For example, you could use a $(l:bloodmagic:demon_will/item_routing/standard_item_filter)Standard Item Filter$() set to $(item)Iron Ore$() on top of a $(item)Furnace$(), a second filter set to coal on the side of the furnace, and an Input Node underneath set to Iron Ingots."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/routing/node_demo.png"], "border": true, "text": "An Output Node in-world."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/routing/node_gui_right.png"], "border": true, "text": "Once we've got our nodes set up, let's open up the Node GUI."}, {"type": "patchouli:text", "text": "Over on the right, we have our cardinal directions - $(bold)D$()own, $(bold)U$()p, $(bold)N$()orth, $(bold)S$()outh, $(bold)W$()est, and $(bold)E$()ast. You'll notice that a little picture of a block appears on some buttons, representing what's on that side of the node. The GUI will open up on the side facing an attached inventory, or Down if no inventory is present. The buttons themselves follow the direction the player is facing, so the top button is 'forwards', the left button is 'left', etcetera."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/routing/node_gui_left.png"], "border": true}, {"type": "patchouli:text", "text": "Over on the left, you'll see a space for us to insert a Filter for the selected side, and a Priority. bigger numbers = more important. $(br2)Nodes can have one filter per side - so we'll select the side we want, and put our filter in it. (For more information on Filters, see 'A Primer On Filters')"}, {"type": "patchouli:text", "text": "Once your Input and Output Nodes have been set up, have been linked together with the help of a $(l:bloodmagic:demon_will/item_routing/node_router)Node Router$() in a network that includes exactly one a $(item)Master Routing Node$(),  and both have item filters inserted to the correct sides, you should be good to go! Items will be routed according to the priorities and the rules contained within your filters."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/routing/network_demo.png"], "border": true, "text": "Nodes do not have to be linked directly to the $(item)Master Routing Node$(), merely to any node on the network."}]}