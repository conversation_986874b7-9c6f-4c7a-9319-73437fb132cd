{"name": "Tartaric Gems", "icon": "bloodmagic:soulgemgreater", "category": "bloodmagic:demon_will/will_manipulation", "pages": [{"type": "patchouli:text", "text": "$(l:bloodmagic:demon_will/will_manipulation/demon_will)Demon Will$() is a very useful resource, but the fragments you have been getting so far are decidedly lacking in power. What you need is a storage item; A $(item)Tartaric Gem$() seems just the thing. What's more, it can absorb any leftover $(l:bloodmagic:demon_will/will_manipulation/demon_will)Demon Will$() you might have lying around. Just drop them onto the floor and your shiny new gem will absorb them."}, {"type": "bloodmagic:crafting_soulforge", "heading": "<PERSON> <PERSON><PERSON><PERSON>", "recipe": "bloodmagic:soulforge/pettytartaricgem", "text": "Your first gem will hold a maximum of 64 $(l:bloodmagic:demon_will/will_manipulation/demon_will)Will$(). Much more compact than before! $(br2)If you ever want to transfer $(raw)Will$() from one gem to another, simply hold [$(k:use)] while holding the gem you want to empty, and it will transfer its will into the first valid gem it finds in your inventory."}, {"type": "patchouli:text", "text": "Your $(item)Petty Tartaric Gem$() is a useful tool, but it's clearly lacking in power. By carefully working it with $(item)Diamond$(), $(item)Lapis$(), and $(item)Redstone$(), you have found a way to quadruple its storage capabilities."}, {"type": "bloodmagic:crafting_soulforge", "heading": "Lesser Tartaric Gem", "recipe": "bloodmagic:soulforge/lessertartaricgem", "text": "This reinforced gem can hold up to 256 $(l:bloodmagic:demon_will/will_manipulation/demon_will)Will$(). $(br2)Note: You only need one gem when upgrading - the $(item)Hellfire Forge$() will draw $(raw)Will$() from the gem it's crafting before trying to use will from the gem in its Gem Slot. Don't worry, the newly crafted gem will hold any leftover $(raw)Will$() from the process."}, {"type": "patchouli:text", "text": "Your $(item)Lesser Tartaric Gem$() is a noted improvement, but once more you chafe under its limitations. To progress further will involve focusing on your $(l:bloodmagic:altar/blood_altar)Blood Altar$(), as you require the powers of an $(l:bloodmagic:altar/slates)Imbued Slate$(). Combining this slate with your gem and further refining it with another $(item)Diamond$() and a $(item)Block of Gold$(), you have found a way to once again quadruple its storage capabilities."}, {"type": "bloodmagic:crafting_soulforge", "heading": "Common Tartaric Gem", "recipe": "bloodmagic:soulforge/commontartaricgem", "text": "This intricate gem can hold an impressive 1,024 $(l:bloodmagic:demon_will/will_manipulation/demon_will)Will$()."}, {"type": "patchouli:text", "text": "You have clearly outdone yourself with the creation of the $(item)Common Tartaric Gem$(), but you feel there is still more you can do. However, getting more out of your gem will involve the culmination of all your work so far. Not only do you need a $(l:bloodmagic:altar/slates)Demonic Slate$(), you also require a $(l:bloodmagic:utility/alchemical_reaction_chamber#blood_shard)Weak Blood Shard$() $(o)and$() a $(l:bloodmagic:demon_will/will_manipulation/crystallized_will)Demon Will Crystal$(). Of course, it will come with rewards to match, powering your $(l:bloodmagic:demon_will/demonic_items/sentient_tools)Sentient Tools$() like nothing you have seen before..."}, {"type": "bloodmagic:crafting_soulforge", "heading": "Greater Tartaric Gem", "recipe": "bloodmagic:soulforge/greatertartaricgem", "text": "This masterpiece of artifice can hold an astounding 4,096 $(l:bloodmagic:demon_will/will_manipulation/demon_will)Will$()."}, {"type": "patchouli:text", "flag": "mod:curios", "text": "As you have the $(thing)Curios API$() installed, you can equip $(item)Tartaric Gems$() as a necklace. If you want to wear more curios at once, consider using a $(l:bloodmagic:alchemy_array/sigil/holding)Sigil of Holding$(), or the $(l:bloodmagic:alchemy_array/living_equipment/armor_upgrades/curios_sockets)Socketed Upgrade$() for your $(l:bloodmagic:alchemy_array/living_equipment/living_basics)Living Armour$()."}]}