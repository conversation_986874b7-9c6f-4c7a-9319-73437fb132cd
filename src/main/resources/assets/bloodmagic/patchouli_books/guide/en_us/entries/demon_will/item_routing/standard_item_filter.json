{"name": "Standard Item Filter", "icon": "bloodmagic:itemrouterfilterexact", "category": "bloodmagic:demon_will/item_routing", "pages": [{"type": "patchouli:text", "text": "The Standard Item Filter lets you select up to 9 items to withdraw from, or insert into, the adjacent inventory when inserted into a $(l:bloodmagic:demon_will/item_routing/routing_nodes)Routing Node$(). $(br2)Each item has a quantity - leaving this blank will default to 'all'. $(br2)The Filter also has an Allow and Deny function. In Deny mode, quantities are ignored. "}, {"type": "bloodmagic:crafting_alchemy_table", "heading": "Standard Item Filter", "recipe": "bloodmagic:alchemytable/router_filter", "text": "$(br)When used in an $(item)Input Routing Node$(), the quantity tells the node how many of that item to leave in the selected inventory. Anything above this amount will be imported into the network."}, {"type": "patchouli:text", "text": "When used in an $(item)Output Routing Node$(), the quantity tells the node how many of that item to fill in the selected inventory with. Anything above this amount will be left in the network - either passed into another valid inventory, or left where it is."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/routing/standard_item_filter_gui.png", "bloodmagic:images/entries/routing/standard_item_filter_mouseover.png"], "title": "Standard Item Filter GUI", "border": true, "text": "The GUI and the mouseover text of a configured filter."}]}