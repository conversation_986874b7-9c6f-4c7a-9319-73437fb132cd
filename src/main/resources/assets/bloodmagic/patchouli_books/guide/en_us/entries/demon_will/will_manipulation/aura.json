{"name": "<PERSON>", "icon": "bloodmagic:demoncrucible", "category": "bloodmagic:demon_will/will_manipulation", "pages": [{"type": "patchouli:text", "text": "As we have $(l:bloodmagic:demon_will/will_manipulation/demon_will)established$(), $(raw)Demonic Will$() coalesces around certain creatures and propels them with malevonent force. However, this is not the only place that $(raw)Will$() can exist. By burning $(raw)Will$() in a $(item)Demon Crucible$(), it is possible to unleash the will into the $(thing)Aura$(), to great and fascinating effect."}, {"type": "bloodmagic:crafting_soulforge", "heading": "Demon Crucible", "recipe": "bloodmagic:soulforge/demon_crucible", "text": "Put a charged $(l:bloodmagic:demon_will/will_manipulation/soul_gem)Tartaric Gem$() or any aspect of $(item)Demon Will$() or $(item)Demon Crystal$() into it and let it run. $(item)Crystals$() will be consumed once the chunk's $(raw)Will$() dips below 50, wheras $(raw)Demon Will$() (in item form or from the $(item)Tartaric Gem$()) will be consumed a bit at a time, as needed."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/demon_will/demon_crucible.png"], "title": "Demon Crucible", "border": true, "text": "The $(item)Demon Crucible$(), with a $(l:bloodmagic:demon_will/will_manipulation/soul_gem)Tartaric Gem$() inside it."}, {"type": "patchouli:text", "text": "Now we have $(raw)Raw Will$() in the atmosphere. Great, now what? $(br2)Some $(thing)Rituals$() $(l:bloodmagic:rituals/ritual_tinkerer)benefit$() from $(raw)Raw Will$(), but the main benefit from this is the ability to create refined $(item)Demon Will Crystals$() and from there, $(l:bloodmagic:rituals/ritual_list/ritual_crystal_split)split them$() into their $(l:bloodmagic:demon_will/will_manipulation/aspected_will)Aspects$(). $(br2)Once you have some of these $(item)Aspected Will Crystals$(), you can burn them once more in the $(item)Crucible$() to unleash them into the $(thing)Aura$() for your rituals to benefit from."}, {"type": "patchouli:text", "text": "It's important to mention that the $(thing)Aura$() is chunk-based. That is, any $(raw)Will$() you burn will fill up the chunk that you burn it in, up to a total cap of 100 for each type. You can measure this using a $(l:bloodmagic:demon_will/will_manipulation/aura_gauge)Demon Will Aura Gauge$(). $(br2)To move Demon Will around, simply place a $(item)Demon Pylon$() in any adjacent chunk, and $(raw)Will$() will be drawn towards it."}, {"type": "bloodmagic:crafting_soulforge", "heading": "Demon Pylon", "recipe": "bloodmagic:soulforge/demon_pylon", "text": "The Pylon will draw in all kinds of will from all adjacent chunks (not including diagnonals). Multiple Pylons can be chained in order to transfer Will over larger distances."}, {"type": "patchouli:text", "text": "If you place a $(l:bloodmagic:demon_will/will_manipulation/soul_gem)Tartaric Gem$(), inside a $(l:bloodmagic:demon_will/will_manipulation/soul_forge)Hellfire Forge$() will cause it to rapidly absorb $(raw)Will$() from the chunk it's in. $(br2)With an Aspect of $(raw)Will$() in the chunk, such as $(raw)Raw$() or $(corrosive)Corrosive$(), and an empty $(item)Tartaric Gem$(), it's possible to fill the Gem with that Aspect. $(br2)This lets you modify your $(l:bloodmagic:demon_will/demonic_items/sentient_tools)Sentient Tools$() and $(l:bloodmagic:demon_will/demonic_items/sentient_sword)Sword$() accordingly. The effects are defined $(l:bloodmagic:demon_will/will_manipulation/aspected_will#sentient_aspects)here$()."}, {"type": "patchouli:relations", "title": "Related Links", "entries": ["bloodmagic:rituals/ritual_tinkerer", "bloodmagic:demon_will/will_manipulation/crystallized_will", "bloodmagic:demon_will/will_manipulation/aspected_will", "bloodmagic:rituals/ritual_list/ritual_crystal_split", "bloodmagic:rituals/ritual_list/ritual_crystal_harvest"], "text": "There's more I can do, I can feel it..."}]}