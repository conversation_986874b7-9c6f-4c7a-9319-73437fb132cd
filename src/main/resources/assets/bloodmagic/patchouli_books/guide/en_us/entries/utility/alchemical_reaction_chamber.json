{"name": "Alchemical Reaction Chamber", "icon": "bloodmagic:alchemicalreactionchamber", "category": "bloodmagic:utility", "pages": [{"type": "patchouli:text", "text": "The $(item)Alchemical Reaction Chamber$() isn't fully implemented yet, but among other things it can function as a $(thing)Furnace$(), offers a form of $(l:bloodmagic:utility/ore_processing)Ore-Tripling$(), can revert $(l:bloodmagic:altar/soul_network)Blood Orbs$(), $(item)Netherrite$() and $(item)Reinforced Runes$(), and is currently the only way to get $(blood)Weak Blood Shards$(), specifically from a $(l:bloodmagic:dungeons/tau_fruit)Saturated Tau$()."}, {"type": "patchouli:crafting", "recipe": "bloodmagic:arc"}, {"type": "bloodmagic:crafting_soulforge", "heading": "<PERSON><PERSON><PERSON>", "recipe": "bloodmagic:soulforge/sanguine_reverter", "anchor": "blood_shard", "text": "The $(item)Sanguine Reverter$() is used to create $(blood)Weak Blood Shards$(), and revert $(l:bloodmagic:altar/soul_network)Blood Orbs$(), $(item)Netherrite$() and $(item)Reinforced Runes$() to their input crafting item."}, {"type": "bloodmagic:3x_crafting_arc", "a.heading": "Weak <PERSON>", "a.recipe": "bloodmagic:arc/weakbloodshard_tau", "b.heading": "<PERSON><PERSON>", "b.recipe": "bloodmagic:arc/reversion/weak_blood_orb", "c.heading": "<PERSON>ert Apprentice Orb", "c.recipe": "bloodmagic:arc/reversion/apprentice_blood_orb"}, {"type": "bloodmagic:3x_crafting_arc", "a.heading": "Revert <PERSON><PERSON>", "a.recipe": "bloodmagic:arc/reversion/magician_blood_orb", "b.heading": "Revert <PERSON>", "b.recipe": "bloodmagic:arc/reversion/master_blood_orb", "c.heading": "<PERSON>ert netherrite", "c.recipe": "bloodmagic:arc/reversion/netherite_ingot"}, {"type": "bloodmagic:crafting_arc", "heading": "<PERSON><PERSON>d <PERSON>", "recipe": "bloodmagic:arc/reversion/speed", "text": "All Reinforced Runes can be reverted in this way - this is just a demonstration."}, {"type": "patchouli:crafting", "heading": "Hydration Cell", "recipe": "bloodmagic:primitive_hydration_cell", "text": "The Hydration Cell is used to make Clay, the Cornerstone of Balance."}, {"type": "bloodmagic:2x_crafting_arc", "a.heading": "Clay from Sand", "a.recipe": "bloodmagic:arc/clay_from_sand", "a.fluid_input": "minecraft:water_bucket", "b.heading": "Clay from Terracotta", "b.recipe": "bloodmagic:arc/clay_from_terracotta", "b.fluid_input": "minecraft:water_bucket"}, {"type": "patchouli:text", "heading": "Automation", "text": "Should you wish to automate the ARC, perhaps as part of an $(l:bloodmagic:utility/ore_processing)Ore-Tripling$() chain, you may want to know that it is $(thing)sided$(), much like a $(item)furnace$(). Tools can only be inserted or extracted from the top, inputs from the sides, and outputs from the bottom. Keep this in mind when placing your $(item)Hoppers$() or $(l:bloodmagic:demon_will/item_routing/routing_nodes)Routing Nodes$()."}]}