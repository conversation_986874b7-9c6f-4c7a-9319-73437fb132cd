{"name": "<PERSON><PERSON>", "icon": "bloodmagic:itemrouterfiltermoditems", "category": "bloodmagic:demon_will/item_routing", "pages": [{"type": "patchouli:text", "text": "The $(item)Mod Item Filter$() lets you select up to 9 items from different mods. Similarly to the $(l:bloodmagic:demon_will/item_routing/standard_item_filter)Standard Item Filter$(), it has a quantity selector and an allow/deny function. Leaving the quantity blank defaults to 'all'. $(br2)For each item that you put into this filter, $(thing)any item from the same mod$() will be matched."}, {"type": "bloodmagic:crafting_alchemy_table", "heading": "<PERSON><PERSON>", "recipe": "bloodmagic:alchemytable/mod_router_filter", "text": "This allows you to deny/permit entire swathes of items. Handy for sorting all of your $(blood)Blood Magic$() items into their own super-special chest, to name an example at random."}]}