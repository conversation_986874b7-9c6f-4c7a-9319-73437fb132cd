{"name": "Penance of the Leaden Soul", "icon": "bloodmagic:upgradescraps", "category": "bloodmagic:rituals/ritual_list", "extra_recipe_mappings": {"bloodmagic:upgradescraps": 7}, "pages": [{"type": "multiblock", "name": "Penance of the Leaden Soul", "multiblock_id": "bloodmagic:downgrade", "text": "Use a $(l:bloodmagic:rituals/ritual_diviner#dusk)Ritual Diviner [Dusk]$(/l) for easier construction."}, {"type": "bloodmagic:ritual_info", "ritual": "downgrade"}, {"type": "patchouli:text", "anchor": "downgrades", "text": "This ritual consumes excess $(thing)Upgrade Points$() (in the form of $(item)Tomes$(), $(item)Scraps$(), and $(item)Synthetic Upgrade Points$()) alongside one  $(item)Key Item$() per level, in order to apply Downgrades to your worn $(l:bloodmagic:alchemy_array/living_equipment/living_basics)Living Armour$(). The key item is different for each downgrade, and is pictured in each Downgrade Entry. These downgrades will harshly limit your abilities, but will provide you with a wealth of additional $(thing)Upgrade Points$() to play around with, allowing for much more specialisation than was previously available to you."}, {"type": "patchouli:crafting", "recipe": "bloodmagic:synthetic_point", "text": "If you do not have enough points avaliable to you, you can craft Synthetic Upgrade Points. Each one of these is worth a single Upgrade Point. "}, {"type": "patchouli:text", "text": "To use the ritual, simply place your tomes (or other sources of Upgrade Points) and the required Item(s) for the particular downgrade you have in mind into the attached chest (see the previous page), while wearing your Living Armour. The ritual will consume the points and apply the downgrade to your armour. The order of consumption is $(item)Living Armour Upgrade Scraps$(), then $(item)Living Armour Upgrade Tomes$(), and finally $(item)Synthetic Upgrade Points$()."}, {"type": "patchouli:text", "text": "The downgrades applied to your armour depends on what Item(s) you place in the chest. Each item that matches a particular downgrade will increase the desired downgrade's level by one. In this manner, multiple downgrades can be applied at once with a single ritual activation. So if you wanted, say, Battle Hungry level 3, you'd put 3 $(item)Rotten Flesh$() and items worth 35 $(thing)Upgrade Points$() into the chest, and activate the ritual whilst wearing a full set of $(thing)Living Armour$()."}, {"type": "patchouli:text", "text": "The ritual will respect any settings you have configured on your $(l:bloodmagic:alchemy_array/living_equipment/training_bracelet)Living Armour Training Bracelet$(), so if you have it set to 'do not allow other upgrades' then the ritual will fail to apply the downgrades to you. If in doubt, put your Training Bracelet away!"}, {"type": "patchouli:spotlight", "item": "bloodmagic:upgradescraps", "title": "LA Upgrade Scraps", "text": "Excess points will be returned in the form of $(item)Living Armour Upgrade Scraps$(), which can be used in subsequent rituals."}]}