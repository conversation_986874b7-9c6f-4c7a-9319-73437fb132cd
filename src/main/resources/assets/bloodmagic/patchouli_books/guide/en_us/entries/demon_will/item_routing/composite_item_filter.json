{"name": "Composite Item Filter", "icon": "bloodmagic:itemrouterfiltercomposite", "category": "bloodmagic:demon_will/item_routing", "pages": [{"type": "patchouli:text", "text": "The $(item)Composite Item Filter$() does nothing on its own, but when combined with another type of $(item)Filter$(), it allows you to apply that filter's rules to it. $(br2)This means you can, for example, use the Enchantment Filter's 'Any Enchantments' and the Tag Filter's 'forge:swords' to only allow enchanted swords to pass through."}, {"type": "patchouli:text", "text": "Similarly to the $(l:bloodmagic:demon_will/item_routing/standard_item_filter)Standard Item Filter$(), it has a quantity selector and an allow/deny function. Leaving the quantity blank defaults to 'all'. Other buttons will appear on the GUI as you combine it with other filters."}, {"type": "bloodmagic:2x_crafting_alchemy_table", "a.heading": "Composite Item Filter", "a.recipe": "bloodmagic:alchemytable/composite_router_filter", "b.heading": "Adding A Tag Filter", "b.recipe": "bloodmagic:alchemytable/filter/composite_tag_filter"}, {"type": "bloodmagic:2x_crafting_alchemy_table", "a.heading": "Adding An Enchant Filter", "a.recipe": "bloodmagic:alchemytable/filter/composite_enchant_filter", "b.heading": "Adding A Mod Filter", "b.recipe": "bloodmagic:alchemytable/filter/composite_mod_filter"}]}