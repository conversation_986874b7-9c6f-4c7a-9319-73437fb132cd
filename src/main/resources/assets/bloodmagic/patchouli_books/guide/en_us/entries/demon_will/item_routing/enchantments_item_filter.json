{"name": "Enchantments <PERSON>em <PERSON>lter", "icon": "bloodmagic:itemrouterfilterenchant", "category": "bloodmagic:demon_will/item_routing", "pages": [{"type": "patchouli:text", "text": "The $(item)Enchantments Item Filter$() lets you sort items via any $(thing)Enchantments$() that they may (or may not) have. It operates similarly to the $(l:bloodmagic:demon_will/item_routing/standard_item_filter)Standard Item Filter$(), particularly with regards to the quantity and allow/deny functions, but with a few extra buttons."}, {"type": "bloodmagic:crafting_alchemy_table", "heading": "Enchantments <PERSON>em <PERSON>lter", "recipe": "bloodmagic:alchemytable/enchant_router_filter", "text": "Any enchanted book will work for this recipe."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/routing/enchant_item_filter_gui.png"], "title": "Enchantments Filter GUI", "border": true, "text": "Note the two new buttons to the right of the 'Allow' button."}, {"type": "patchouli:text", "text": "The first button allows you to select whether to match $(thing)Every Enchantment$() on an enchanted item, $(thing)Any Enchantment$(), or $(thing)one particular enchantment$(). The second button allows you to specify whether to pay attention to the level or not. (E.G. 'Protection III' versus 'Protection'). $(br2)If you insert $(thing)an unenchanted item$() into the filter, you can effectively Allow or Deny $(thing)any sort of enchantment$() to be inserted into or removed from the specified inventory."}]}