{"name": "Incense Altar", "icon": "bloodmagic:incensealtar", "category": "bloodmagic:utility", "pages": [{"type": "patchouli:text", "text": "The $(item)Incense Altar$() is a multiblock structure that can be used to boost your self-sacrificing at a $(l:bloodmagic:altar/blood_altar)Blood Altar$(). By standing near your setup, the $(item)Incense Altar$() will calm your soul based on the area's total $(thing)Tranquility$(), allowing you to significantly increase your $(blood)LP$() gains."}, {"type": "patchouli:crafting", "recipe": "bloodmagic:incense_altar"}, {"type": "patchouli:text", "text": "The basic Tier 1 setup of an $(item)Incense Altar$() is the Altar itself; place it down anywhere (You may want to set up a 3x3 block platform, this will be helpful later) and stay within a 5 block radius of the Altar. $(br2)While the Altar is working, it will emit flame particles from its top and transform your $(l:bloodmagic:altar/blood_altar#knife)Sacrificial Knife$(). Once your knife starts to shine, holding and releasing right click near a $(l:bloodmagic:altar/blood_altar)Blood Altar$(). will sacrifice 90% of your health all at once."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/utility/incense_altar1.png"], "title": "Basic Setup", "border": true, "text": "Basic setup showing the 3x3 square of blocks before the path blocks."}, {"type": "patchouli:text", "text": "What's more, based on the total $(thing)Tranquility$() of the area, you will receive a bonus to the $(blood)LP$() added to the $(l:bloodmagic:altar/blood_altar)Blood Altar$(). Hovering over the $(item)Incense Altar$() with either a $(l:bloodmagic:alchemy_array/sigil/divination)Divination Sigil$(/l) or $(l:bloodmagic:alchemy_array/sigil/seer)Seer's Sigil$() will display the total $(thing)Tranquility$() of the setup (top number) and the percentage bonus received when sacrificing (bottom number). When you sacrifice, it will take the $(blood)LP$() that you would normally get and multiply it by (1 + bonus/100)."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/utility/incense_altar2.png"], "title": "Incense HUD", "border": true, "text": "Incense HUD, default in top left corner, showing a self-sacrifice bonus of +20%."}, {"type": "patchouli:text", "text": "\"But Way, how can I increase this bonus?\" Why, by increasing the $(thing)Tranquility$() of the surrounding area! ...That may be a bit ambiguous. $(br2)To increase the $(thing)Tranquility$() of the area, you must place paths leading out from your $(item)Incense Altar$(). These paths need to be constructed from a three wide set of $(item)Path$() blocks, such as the $(item)Wooden Path$(), that extend from the 3x3 set of solid reference blocks in all four cardinal directions."}, {"type": "patchouli:crafting", "recipe": "bloodmagic:path/path_wood", "recipe2": "bloodmagic:path/path_woodtile"}, {"type": "patchouli:crafting", "recipe": "bloodmagic:path/path_stone", "recipe2": "bloodmagic:path/path_stonetile"}, {"type": "patchouli:crafting", "recipe": "bloodmagic:path/path_wornstone", "recipe2": "bloodmagic:path/path_wornstonetile"}, {"type": "patchouli:crafting", "recipe": "bloodmagic:path/path_obsidian", "recipe2": "bloodmagic:path/path_obsidiantile"}, {"type": "patchouli:text", "text": "Each new \"ring\" of path blocks follow a set of rules: $(li)All path blocks on the same ring have to be on the same y-level.$(li)The next ring of path blocks may not be more than 5 blocks higher/lower than the previous ring.$(li)The blocks that are the same level or up to two blocks above the path blocks' ring count towards the total $(thing)Tranquility$() - these are indicated by the glass blocks in the picture."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/utility/incense_altar.png"], "title": "Incense Altar", "border": true, "text": "Incense Altar with different Path orientations."}, {"type": "patchouli:text", "text": "Furthermore:$(li)The efficacy of each type of path block only lasts a certain distance: wooden paths can only go three rings out from the centre, stone paths for up to five rings, worn stone paths for seven rings, and obsidian paths for nine rings.$(bn2)  Now obviously, not every type of block will count towards your $(thing)Tranquility$(). Not just any ol' cobble will do, no! We need crops, dirt, and even... lava?"}, {"type": "patchouli:text", "text": "There are multiple block categories that count towards the total $(thing)Tranquility$(). In no particular order, they are: Plants, Crops, Trees, Earthen, Water, Fire, and Lava. The $(item)Incense Altar$() will look at all of the blocks that are within its range (set by the path blocks) and tabulate how much total $(thing)Tranquility$() of each Type there is. Once done, it will calculate the total $(thing)Tranquility$() by square-rooting the $(thing)Tranquility$() of each type and then adding them together."}, {"type": "patchouli:text", "text": "This means that for later-game setups, it is best to have many different types of $(thing)Tranquility$(). Although other mods may add their own, and more will be added later, the blocks that contribute to $(thing)Tranquility$() are: Lava, Water (including most Waterlogged blocks), Life Essence, Netherrack, Dirt, Farmland, Potatoes, Carrots, Wheat, Nether Wart, Beetroots, Leaves, Logs, Fire, and Grass."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/utility/incense_altar3.png"], "title": "Incense Altar Setup", "border": true, "text": "Late-game Incense Altar setup with many different levels."}, {"type": "patchouli:text", "text": "Of course, your setup can be as steep or as shallow as you want (Within reason, as defined earlier in this entry). $(br2) It is important to note that the $(thing)Tranquility bonus$() is capped by the size of your Altar (and thus, the tier of path you are using). The caps are as follows: $(li) No Path: 20%. $(li) Wooden Path: 60%. $(li) Stone Path: 120%. $(li) Worn Stone Path: 200%. $(li) Obsidian Path: 300%."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/utility/incense_altar4.png"], "title": "Incense Altar Setup", "border": true, "text": "A very simple $(item)Incense Altar$() setup. Note the (optional) mixing of different path blocks."}]}