{"name": "Upgrades", "icon": "bloodmagic:mastercore", "category": "bloodmagic:demon_will/item_routing", "pages": [{"type": "patchouli:text", "text": "Currently, only one $(thing)Upgrade$() is available for the Routing Network. $(br2)The $(item)Basic Routing Logic Upgrade$() increases the total amount of items transferred per operation. (once per second, currently). By default, the network will transfer 16 items at a time, but each of these will raise that cap by 8."}, {"type": "bloodmagic:crafting_soulforge", "heading": "Basic Routing Logic Upgrade", "recipe": "bloodmagic:soulforge/master_node_upgrade", "text": "These upgrades stack to 16, allowing for a sum total of 144 items to be transferred per second. Note that this can be split over multiple stacks, if many different items need to be transferred at once."}]}