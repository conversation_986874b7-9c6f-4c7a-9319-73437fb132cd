{"name": "Training Bracelet", "icon": "bloodmagic:upgradetrainer", "category": "bloodmagic:alchemy_array/living_equipment", "pages": [{"type": "patchouli:text", "text": "This new equipment is an incredible help, but its undirected growth can sometimes be... frustrating. To this end, you have devised a form of $(item)Training Bracelet$() to assist you in your endeavours. Once crafted, a simple press of the [$(k:use)] will activate its menu and allow you to specify which abilities to focus your attention on... or which ones to avoid."}, {"type": "bloodmagic:crafting_array", "heading": "Training Bracelet", "recipe": "bloodmagic:array/living_trainer", "text": "$(italic)*Insert Rocky Training Montage here*$()$(br2)Only one of these bracelets will work at a time. Off-hand > Curios (if available) > Main Inventory (including main hand) > add-on inventories."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/alchemy_array/living_equipment/training_bracelet.png"], "title": "Training Bracelet GUI", "border": false, "text": "The Training Bracelet's Menu - it can keep track of up to 16 different upgrades at once."}, {"type": "patchouli:text", "text": "The bracelet can specify a limit for any given upgrade (assuming you have a copy of the $(item)Tome$() to hand). For example, you could tell it to limit Strong Legs to level 3 - once you reach this level, Strong Legs will no longer gain experience. It can also prevent or allow the training of all other skills that you haven't otherwise specified. If you want to allow all upgrades except one, you can add that one to the bracelet in 'allow others' mode and set its level cap to 0."}]}