{"name": "Redstone and Automation", "icon": "minecraft:redstone", "category": "bloodmagic:altar", "pages": [{"type": "patchouli:text", "text": "The $(l:bloodmagic:altar/blood_altar)Blood Altar$() is a fantastic tool, but standing around and waiting for slates to craft is not your idea of a good time. Luckily, items and $(blood)LP$() can be automatically piped in and out of the altar, albeit with a few caveats. $(br2)While a simple $(item)Hopper$() lets you pipe items in, the Altar won't stop it from inputting more than 1 at a time. It will happily craft 64 slates in one"}, {"type": "patchouli:text", "text": "go, consuming 64 times as much $(blood)LP$() as usual to do so - but if you can't supply said $(blood)LP$() fast enough, you're going to run into trouble. $(br2)Additionally, the altar makes no distinction between input and output, so without some sort of filter, items will be pulled in and out as fast as your item transfer system can handle. Perhaps a look at the $(l:bloodmagic:demon_will/item_routing/routing_nodes)Routing Nodes$() will be helpful..."}, {"type": "patchouli:text", "text": "The altar also supports the transfer of $(blood)Life Essence$(), both to and from an external tank. Simply hook up your fluid pipe of choice and you can store excess $(blood)Life Essence$() for later crafts. $(br2)Note that the transfer speed is very slow by default. If you want to speed it up, you'd best look into $(l:bloodmagic:altar/blood_rune/acceleration_rune)Acceleration Runes$() and $(l:bloodmagic:altar/blood_rune/dislocation_rune)Displacement Runes$()."}, {"type": "patchouli:text", "text": "Don't forget that this $(blood)Life Essence$() isn't taken directly from the $(l:bloodmagic:altar/blood_altar)altar$() itself, but rather from a second, secret internal tank. This tank can hold $(thing)up to 10%$() of the $(blood)Life Essence$() that the altar itself can, so if the numbers don't appear to be adding up exactly right, or if $(blood)Life Essence$() appears to be vanishing from your altar, this is probably where it's going. The same limitations apply to $(blood)Life Essence$() being piped in."}, {"type": "patchouli:text", "text": "The amount of $(blood)Life Essence$() in the $(l:bloodmagic:altar/blood_altar)Blood Altar$() can be read via a comparator on the side, similarly to a vanilla chest. $(br2)If you place a $(item)Bloodstone Brick$() underneath the altar, the comparator will instead read the value of the $(l:bloodmagic:altar/soul_network)Soul Network$(/l) of the owner of any orb that is placed into the Altar. $(br2)The signal strength depends on the size of the orb in the altar, not the maximum $(blood)LP$() of the network."}, {"type": "patchouli:text", "text": "For example, if you have $(blood)500,000 LP$(), a Weak Blood Orb would show as completely full, but a Master Blood Orb would show as only half full. This can be used to, for example, deactivate certain rituals when you are running low on $(blood)LP$(), to ensure you don't run out. $(br2)Lastly, placing a $(item)Redstone Lamp$() underneath the altar will make it output a redstone signal upon finishing a crafting operation."}]}