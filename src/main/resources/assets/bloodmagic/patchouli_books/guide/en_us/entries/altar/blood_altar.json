{"name": "The Blood Altar", "icon": "bloodmagic:altar", "category": "bloodmagic:altar", "priority": "true", "extra_recipe_mappings": {"bloodmagic:daggerofsacrifice": 13}, "pages": [{"type": "patchouli:text", "text": "The $(item)Blood Altar$() is the central block of the mod, able to convert raw $(blood)blood$() into pure $(blood)Life Essence$(). While it may start off small and insignificant, its strength and size grows throughout the mod, acting as a cornerstone for most of your power."}, {"type": "patchouli:crafting", "recipe": "bloodmagic:blood_altar"}, {"type": "patchouli:text", "text": "When placed into the world, the $(item)Blood Altar$() converts $(blood)blood$() into $(blood)Life Essence$(), which it then uses to transfigure items placed into it. By pressing [$(k:use)] while looking at the Altar, you may insert one item from your hand into the Altar's internal inventory. pressing [$(k:use)] with an empty hand will extract the item."}, {"type": "multiblock", "name": "Tier 1 Blood Altar", "multiblock_id": "bloodmagic:altar_one", "text": "The Tier 1 Blood Altar, which has no runes."}, {"type": "patchouli:text", "text": "In order for you to add $(blood)Life Essence$(), measured as \"$(blood)LP$()\", you first have to craft a $(item)Sacrificial Knife$(). By pressing [$(k:use)] while aiming at air with the knife, you can \"extract\" $(blood)200 LP$() for the cost of one heart, placing it into a nearby Altar. The Altar starts with a maximum capacity of 10,000LP, and the blood level in the basin indicates the percentage filled. The $(l:bloodmagic:alchemy_array/sigil/divination)Divination Sigil$(/l) allows more detailed information about the Altar."}, {"type": "patchouli:crafting", "recipe": "bloodmagic:sacrificial_dagger", "anchor": "knife", "text": "Keep in mind that 10% of the total $(blood)LP$() the altar can hold will be absorbed into an invisible internal 'tank' used for extracting and inserting $(blood)Life Essence$() into the Altar."}, {"type": "patchouli:text", "text": "The Blood Altar will attempt to start to craft as soon as an item is placed inside by a player (or after a periodic 5 seconds). The $(blood)LP$() inside of the Altar will slowly drain (indicated by red particles), transforming the item. If there is no $(blood)LP$() in the Altar, gray smoke will appear to indicate that the Altar is losing progress instead. Once enough $(blood)LP$() is consumed (cost multiplied by number in the item stack), the full stack will be transformed into a new item."}, {"type": "patchouli:text", "text": "The first item that you will want to craft is a $(l:bloodmagic:altar/soul_network)Weak Blood Orb$(/l), which by default is a diamond plus $(blood)2000 LP$() inside of a Tier 1 Blood Altar. All items that can be crafted by the Blood Altar can be found using Just Enough Items (JEI)."}, {"type": "patchouli:text", "anchor": "blank_rune", "text": "To upgrade the Blood Altar, you need to craft $(item)Blood Runes$() and place them around the Altar. Blood Runes act as upgrades to the Altar, and by using more advanced versions of the Blood Runes you can confer different effects on the Altar. The basic version, the $(item)Blank Rune$(), does not give any upgrades - it's only use is to upgrade the Tier of the Altar."}, {"type": "patchouli:crafting", "recipe": "bloodmagic:blood_rune_blank"}, {"type": "patchouli:text", "text": "In order to upgrade the Blood Altar to Tier 2, you must place 8 $(item)Blood Runes$() around the Altar. The runes in the cardinals can be upgraded, but the corner runes cannot act as upgrade runes until Tier 3."}, {"type": "multiblock", "name": "Tier 2 Blood Altar", "multiblock_id": "bloodmagic:altar_two", "text": "The Tier 2 Blood Altar, which has 8 total runes."}, {"type": "patchouli:text", "anchor": "dagger_of_sacrifice", "text": "Now that you have a Tier 2 Altar, you can look into getting $(blood)Life Essence$() from somewhere other than yourself. The $(item)Dagger of Sacrifice$() will allow you to sacrifice any mob (monster or passive) that stands within 2 blocks of your Altar, instantly killing them and granting you a decent sum of $(blood)LP$(). You can increase the amount you get per kill with $(l:bloodmagic:altar/blood_rune/sacrifice_rune)Runes of Sacrifice$(). Different entities give different amounts of $(blood)LP$(). Check your configs for more info."}, {"type": "bloodmagic:crafting_altar", "heading": "Dagger of Sacrifice", "recipe": "bloodmagic:altar/daggerofsacrifice", "text": "Slaughtering villagers for fun and profit!"}, {"type": "patchouli:text", "text": "To upgrade the Blood Altar to Tier 3, place 5 $(item)Blood Runes$() one block down and two blocks away from the previous set of runes along each edge. Then place two blocks (indicated by the $(item)Stone Bricks$()) in each corner, starting above the new ring of runes, and then cap each pillar with $(item)Glowstone Blocks$().$(br)To check that it is successfully upgraded, use a $(l:bloodmagic:alchemy_array/sigil/divination)Divination Sigil$(/l) to check the tier. Note that any non-air block can be used for the pillars below the Glowstone."}, {"type": "multiblock", "name": "Tier 3 Blood Altar", "multiblock_id": "bloodmagic:altar_three", "text": "The Tier 3 Blood Altar, which has 28 total runes, 20 more than a Tier 2, 5 on each side."}, {"type": "patchouli:text", "text": "To upgrade the Blood Altar to Tier 4, place 7 $(item) Blood Runes$() one block down and two blocks away from the previous set of runes along each edge. Then place four solid blocks in each corner, starting above the new ring of runes, and then cap each pillar with $(l:bloodmagic:utility/bloodstone_bricks)Bloodstone Bricks$(/l) and/or $(l:bloodmagic:utility/bloodstone_bricks)Large Bloodstone Bricks$(/l). For these, you'll need $(l:bloodmagic:dungeons/tau_fruit)Tau Fruit$(), found via the $(l:bloodmagic:rituals/ritual_list/ritual_simple_dungeon)Edge of the Hidden Realm$() ritual."}, {"type": "multiblock", "name": "Tier 4 Blood Altar", "multiblock_id": "bloodmagic:altar_four", "text": "The Tier 4 Blood Altar, which has 56 total runes, 28 more than a Tier 3, 7 on each side."}, {"type": "patchouli:text", "text": "To upgrade the Blood Altar to Tier 5, place 15 $(item) Blood Runes$() one block down and three blocks away from the previous set of runes along each edge. Leave a one-block gap on either end, then place a $(l:bloodmagic:dungeons/demonite)Hellforged Block$() at each corner. You'll have to go delving deep into the $(thing)Demon Realm$() for this rare and exotic metal."}, {"type": "multiblock", "name": "Tier 5 Blood Altar", "multiblock_id": "bloodmagic:altar_five", "text": "The Tier 5 Blood Altar, which has 108 total runes, 52 more than a Tier 4, 13 on each side."}]}