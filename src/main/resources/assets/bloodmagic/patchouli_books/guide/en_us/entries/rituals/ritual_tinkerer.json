{"name": "<PERSON><PERSON><PERSON>", "icon": "bloodmagic:ritual<PERSON><PERSON>er", "category": "bloodmagic:rituals", "priority": "true", "pages": [{"type": "patchouli:text", "text": "The $(item)Ritual Tinkerer$() is an essential tool for the advanced sanguimancer who is looking for all they can get out of their $(thing)Rituals$(). It has three main modes, as described overleaf. You can cycle between them by pressing [$(k:sneak)] and [$(k:use)]."}, {"type": "patchouli:crafting", "recipe": "bloodmagic:ritual_reader"}, {"type": "patchouli:text", "text": "$(li)Information: Describes the function of the $(thing)Ritual$(), similar to the $(l:bloodmagic:rituals/ritual_diviner)Ritual Diviner$(). $(li)Set Will Consumed: Tells the $(thing)Ritual$() which kinds of $(raw)Demon Will$() (if any) to consume from the $(l:bloodmagic:demon_will/will_manipulation/aura)Aura$(). Specify this by carrying $(item)Demon Will Crystals$() in your hotbar, one for each type of $(raw)will$() you wish the $(thing)Ritual$() to consume. Further information about the effects of $(raw)Demon Will$() upon $(thing)Rituals$() can be found on each $(thing)Ritual$()'s respective page in this book."}, {"type": "patchouli:text", "text": "$(li)Define Area: Specifies the zone that the $(thing)Ritual$() should work in, and displays the current zone. If multiple zones can be specified, pressing [$(k:sneak)] and [$(k:use)] on the $(item)Master Ritual Stone$() will cycle through them. Some $(thing)Rituals$() can be expanded far beyond their default areas, but keep in mind that this will increase the $(blood)LP$() cost to match..."}]}