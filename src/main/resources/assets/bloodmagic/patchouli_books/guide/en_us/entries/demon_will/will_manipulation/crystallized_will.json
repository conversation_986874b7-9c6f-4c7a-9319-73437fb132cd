{"name": "Crystallized Will", "icon": "bloodmagic:defaultcrystal", "category": "bloodmagic:demon_will/will_manipulation", "extra_recipe_mappings": {"bloodmagic:demoncrucible": 1, "bloodmagic:demoncrystallizer": 1, "bloodmagic:rawdemoncrystal": 1, "bloodmagic:defaultcrystal": 1}, "pages": [{"type": "patchouli:text", "text": "Now that you have plenty of $(l:bloodmagic:demon_will/will_manipulation/demon_will)Demon Will$() in your $(l:bloodmagic:demon_will/will_manipulation/soul_gem)Tartaric Gem$(), it's time to explore what happens when you unleash it upon the world. $(br2)First off, you'll need to get $(raw)Will$() into the $(l:bloodmagic:demon_will/will_manipulation/aura)Aura$().  Next, you'll need to make a $(thing)Demon Crystallizer$()."}, {"type": "bloodmagic:crafting_soulforge", "heading": "Demon Crystallizer", "recipe": "bloodmagic:soulforge/demon_crystallizer", "text": "This will slowly consume $(l:bloodmagic:demon_will/will_manipulation/demon_will)Demon Will$() from the $(l:bloodmagic:demon_will/will_manipulation/aura)Aura$() to produce $(item)Will Crystals$(). The first spire costs 100 $(raw)Will$() to form, and all subsequent spires cost 40 each, but can be burned for 50 in the $(item)Demon Crucible$(), for a net gain of 10. The largest $(item)Crystal Cluster$() can be up to 7 spires."}, {"type": "patchouli:text", "text": "If you have more than 512 total $(raw)Will$() in your inventory (Across any number of $(l:bloodmagic:demon_will/will_manipulation/soul_gem)Tartaric Gems$() and of any one type), you can harvest these crystals by right-clicking the spire with an empty hand. This will remove all but the central spire. $(br2)However, if you do not have enough $(raw)will$(), $(italic)really$() need that central spire's Crystal, or are just in a hurry, you can harvest the whole lot with a pickaxe."}]}