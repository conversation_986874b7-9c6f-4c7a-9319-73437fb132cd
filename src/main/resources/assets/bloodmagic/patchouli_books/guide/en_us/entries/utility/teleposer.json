{"name": "Teleposers", "icon": "bloodmagic:teleposerfocus", "category": "bloodmagic:utility", "pages": [{"type": "patchouli:text", "text": "$(item)Teleposers$() allow for a form of $(fire)Redstone Controlled$() teleportation. Simply craft a $(item)Teleposition Focus$() (overleaf), bind it to your target teleposer, place it in another teleposer, and apply a redstone signal to the teleposer with a focus in it. Anything - blocks, items, entities, players - in a defined area above the two teleposers will be swapped."}, {"type": "patchouli:crafting", "recipe": "bloodmagic:teleposer", "text": "Nothing comes for free, however; transporting blocks or entities via the teleposer will cost $(blood)1 LP$() each for every two blocks traversed, to a maximum of $(blood)1,000 LP$() per block/entity, or $(blood)10,000 LP$() total."}, {"type": "bloodmagic:crafting_altar", "heading": "Teleposition Focus", "recipe": "bloodmagic:altar/teleposer_focus", "text": "The basic $(item)Teleposer Focus$() will swap anything in a 1x1x1 block area above the two teleposers."}, {"type": "bloodmagic:crafting_altar", "heading": "Enhanced Focus", "recipe": "bloodmagic:altar/enhanced_teleposer_focus", "text": "The $(item)Enhanced Teleposer Focus$() will swap anything in a 3x3x3 block area centred directly above the two teleposers."}, {"type": "patchouli:crafting", "recipe": "bloodmagic:enhanced_teleposer_focus", "text": "The $(item)Reinforced Teleposer Focus$() will swap anything in a 5x5x5 block area centred directly above the two teleposers."}, {"type": "patchouli:text", "text": "Teleposers can be linked one-way (such that a redstone signal to the exiting Teleposer does nothing), two-way (such that each Teleposer has a Focus in it linking to the other teleposer, making you teleport back and forth at a redstone signal from either end), or they can even be chained - A to B to C and back to A. From base-traversing elevator systems to complex underground labrynths, go nuts!"}, {"type": "patchouli:text", "text": "If you're having difficulty getting your Teleposer to work, make sure that it is being $(thing)strongly redstone powered$(). This means that placing a redstone block next to the block won't power it - you'll need redstone dust or a repeater pointing into the side of the Teleposer, or a lever or  button directly on it. Life Essense isn't free after all, so the Teleposer is designed to minimize accidental misfires. For more information on strong vs weak redstone power, please consult $(l:https://minecraft.fandom.com/wiki/Redstone_mechanics?oldid=2127827#Powered_vs._activated)the Minecraft Wiki.$() "}]}