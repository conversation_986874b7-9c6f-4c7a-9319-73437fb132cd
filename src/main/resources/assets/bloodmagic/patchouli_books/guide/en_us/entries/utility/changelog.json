{"name": "Changelog", "icon": "minecraft:map", "category": "bloodmagic:utility", "priority": "true", "pages": [{"type": "patchouli:text", "text": "All the changelogs of $(6)Blood Magic$() to date, from most recent to oldest."}, {"type": "patchouli:text", "title": "3.2.5", "text": "$(li)Fixed it so that the Fishing Rod actually works.$(li)Don't ask."}, {"type": "patchouli:text", "title": "3.2.4", "text": "$(li)Implemented the \"Pathway to the Endless Realm\" ritual, which provides access to a larger version of the Demon Dungeons! $(li2)\"The Endless Realm\" is a sprawling, procedurally generated dungeon, filled with chests bursting with loot and monsters that pack a serious punch! $(li2)Those who successfully navigate its many chambers may find Demonite, an ore that"}, {"type": "patchouli:text", "text": "cannot be found anywhere else. $(li2)The dungeons currently have two distinct regions: $(li3)The \"Entrance Zone\", which contains a wide array of loot and monsters $(li3)The \"Hidden Mines\", containing a bountiful reserve of Demonite, which is used for advanced alchemy and for empowering the Blood Altar to Tier 5. Accessible by finding the \"Foreman's Key\" in the Entrance Zone."}, {"type": "patchouli:text", "text": "$(li2)If you delve deep enough in the dungeon, a \"Spatial Distortion\" may occasionally form; this changes one of the doors within the room to one that leads to a rare dungeon room. $(li)More rooms, room types, and special rooms to come to the Dungeon (eventually (soon (tm)))! $(li)Added Demonite, which can be processed into Hellforged Sand and Hellforged Ingots."}, {"type": "patchouli:text", "text": "$(li)Added more advanced anointments, which offer either increased duration (XL) or a stronger effect (III). $(li)Added the \"Standard Lengthening Catalyst\" and \"Standard Power Catalyst\", which further augment potion effects in the Alchemy Flask. $(li)Added stronger Alchemic Reaction Chamber tools, which are both faster and longer-lasting than previous tools. $(li2)Hellforged Resonator $(li2)Hellforged Explosive Cell $(li2)Advanced Cutting Fluid"}, {"type": "patchouli:text", "text": "$(li2)These tools also boost the chance for bonus outputs when used. For instance, an Intermediate Cutting Fluid will provide 1.5x Iron Sand per Raw Iron, whereas the Advanced Cutting Fluid provides 1.67x.$(br2)$(li)Added Upgraded Explosive Charges and tweaked the power of previous versions. $(li2)Augmented Shaped Charge - breaks 7x7x7 cube of blocks."}, {"type": "patchouli:text", "text": "$(li2)Tunneling Shaped Charge - breaks 5x5x20 long set of blocks. $(li2)Augmented Controlled Charge - breaks up to 8x64 blocks of the same type. $(li2)Augmented Deforester Charge - breaks up to 8x64 logs, clearing away connecting leaves. $(li2)Augmented Fungal Charge - breaks up to 8x64 blocks related to Overworld and Nether big mushrooms."}, {"type": "patchouli:text", "text": "$(li)Added Hellforged Parts, which can only be found through loot within the Hidden Mines. $(li2)Used to reinforce Blood Runes, doubling the effect of the given rune. $(li)Increased the rate of \"Tiny Corrupted Dust\" when using the Resonator in the Alchemic Reaction Chamber. $(li2)Corrupted Dust can be used to increase the yield of the desired ore."}, {"type": "patchouli:text", "text": "$(li)Added a new feature to the Divination and Seer's Sigil: when sneaking and activating the sigil, a GUI will open which will allow you to drag individual HUD elements and save their new locations. $(li)Added new anointments: $(li2)\"Repairing Salve\", which repairs a damaged tool slightly when the tool is used.$(li2)\"Void Essence\", which destroys mundane blocks that are mined."}, {"type": "patchouli:text", "text": "$(li)Also tweaked \"Miner's Secrets\" to only work on exp-providing blocks, while also only being used up when additional exp is provided. $(li)Updated several textures related to vanilla ore processing. $(li)Fixed Blood Altar's delay after crafting. The delay between when an altar finishes a craft and attempts to start a new craft is now 30 ticks. $(li)Fixed Body Builder so that it is properly obtainable."}, {"type": "patchouli:text", "text": "$(li)Fixed Nether Wart handling for the Harvest ritual. $(li)Fixed the Sentient Scythe so that it wasn't made out of paper. Also actually does damage now! $(li)Allowed the Sentient Scythe to receive the Sharpness enchantment while enchanting. $(li)Changed the Ritual of the Feathered Knife to interact with the \"Tough Palms\" Living Upgrade."}, {"type": "patchouli:text", "text": "$(li)Added Amethyst Throwing Daggers, which do the same damage as Iron Throwing Daggers. $(li2)Can be combined in an ARC with a Lingering Alchemy Flask to add potion effects to the thrown daggers. $(li)Added the reworked ritual \"Rhythm of the Beating Anvil\", which can autocraft vanilla recipes. When augmented, can also simplify automation of Hellfire Forge and Alchemy Table recipes."}, {"type": "patchouli:text", "text": "$(li)Changed the \"Penance of the Leadened Soul\" so that it respects any currently held Training Bracelets.$(li)Reimplemented recipe for Obsidian Paths."}, {"type": "patchouli:text", "title": "3.2.3", "text": "$(li)Fixed Fortuna Extract so that it properly applied its additional Fortune under certain conditions.$(li)Fixed a crash with the Teleposer that prevented Tile Entities from moving.$(li)Added smelting recipe for copper sand > copper ingot, and blasting furnace recipes for all dusts."}, {"type": "patchouli:text", "title": "3.2.2", "text": "$(li)Fixed in-game guide for real this time."}, {"type": "patchouli:text", "title": "3.2.1", "text": "$(li)Fixed in-game guide so that recipes properly load.$(li)Fixed non-crafting Alchemy Arrays.$(li)Fixed the Mark of the Falling Tower and Ritual of Magnetism to respect the current world's build limit.$(li)Changed a few textures such as the ore fragments."}, {"type": "patchouli:text", "title": "3.2.0", "text": "$(li)Initial release of 1.18.2.$(li)Changed the ARC so that the texture changes based on the tool used and when it is active.$(li)Rebalanced some recipes due to Raw Materials dropping from base ores.$(li)Added copper ore processing.$(li)Added Glowberries and Berry bush handling to the Reap of the Harvest Moon."}, {"type": "patchouli:text", "text": "Known issues:$(li)In-game guide is broken in a few places, primarily when viewing mod recipes such as Blood Altar crafting.$(li)Life Essence blocks do not have the proper overlay when submersed in liquid."}, {"type": "patchouli:text", "title": "3.1.11", "text": "$(li)Reworked and implemented the Potion Flask system! Quite a bit different than previous versions, so make sure to check the guide. $(li)New (and old) non-Vanilla potion effects include: $(li2)Bounce $(li2)Gravity $(li2)Grounded $(li2)Suspended $(li2)Heavy Heart $(li2)Passive"}, {"type": "patchouli:text", "text": "$(li2)Spectral Sight $(li2)Hard Cloak $(li2)Obsidian Cloak $(li2)Flight $(li)Fixed server crash for the Reverence of the Condor ritual. $(li)Reimplemented Teleposers. Hurray! $(li)Reimplemented the Teleposition Sigil. $(li)Reimplemented the Sigil of Suppression. $(li)Reimplemented the Demon Pylon."}, {"type": "patchouli:text", "text": "$(li)Added Will Catalysts, which can be applied to Demon Will Clusters to dramatically increase their rate of growth. $(li)Changed the Hellfire Forge so that it can use Aspected Will in crafting operations. $(li)Modified the Ritual Diviner so that it can cycle through the ritual list backwards by left-clicking in the air. $(li)Added a new item, \"Basic Routing Logic Upgrade\", which increases the total throughput of the Master Routing Node's system."}, {"type": "patchouli:text", "text": "$(li)Reimplemented several rituals: $(li2)Crash of the Timberman $(li2)Dawn of the New Moon $(li2)Ritual of the Satiated Stomach $(li2)Speed Ritual $(li2)Ritual of the High Jump $(li2)Laying of the Filler $(li2)Convocation of the Damned (reworked) $(li2)The Sinner's Burden (renamed from \"Ritual of Grounding\")"}, {"type": "patchouli:text", "text": "$(li2)Mark of the Falling Tower (New meteor system! Oooh, sparkley~!) $(li)Added a new ritual, Yawning of the Void. Used to completely clear large areas of blocks. $(li)Tweaked recipe for Frame Parts to make them cheaper."}, {"type": "patchouli:text", "title": "3.1.10-32", "text": "$(li)Fixed recipe for the \"Brilliance\" Living Upgrade."}, {"type": "patchouli:text", "title": "3.1.10", "text": "$(li)Fixed server-sided issue that prevented recipes from loading for certain types."}, {"type": "patchouli:text", "title": "3.1.9", "text": "$(li)Added the missing recipes for a couple of the Living Armour Downgrades. $(li2)Yes, <PERSON> is indeed blind."}, {"type": "patchouli:text", "title": "3.1.8", "text": "$(li)Made <PERSON><PERSON><PERSON> a required library. $(li)Reimplemented the Penance of the Leadened Soul ritual. Operates slightly differently than before. $(li)Reimplemented the following Living Armour Downgrades: $(li2)Storm Trooper $(li2)Battle Hungry $(li2)Quenched $(li2)Dulled Blade $(li2)Weakened Pick $(li2)Diseased"}, {"type": "patchouli:text", "text": "$(li2)Crippled Arm $(li2)Limp Leg $(li)Reimplemented Repairing $(li)Added the following Living Armour Upgrades/Downgrades: $(li2)Brilliance (Increases armour and toughness values of Living Armour) $(li2)Concrete Shoes (Decreases swim speed) $(li)Decreased hardness of shaped charges."}, {"type": "patchouli:text", "text": "Known issue: Desync between the server and client for the Living Armour chestpiece under certain conditions. Occurs when the item is shift-clicked out of the player's chestpiece slot. Does not occur when clicked without shift. $(li)Only causes a ghost item to appear in the client's inventory. Does not otherwise affect the performance of the armour."}, {"type": "patchouli:text", "title": "3.1.7", "text": "$(li)Fixed another crash with the Living Armour. Maybe I got it this time!"}, {"type": "patchouli:text", "title": "3.1.6", "text": "$(li)Increased range of the Tau plant to 3x1x3 centered on the plant, making it easier to farm the Saturated version. $(li)Fixed crash with the Living Armour under certain conditions.$(li)Fixed the Sacrificial Knife so it respects gravestone mods and other effects that occur on-death. $(li)Fixed the <PERSON>gger of sacrifice so that it similarly respects death events."}, {"type": "patchouli:text", "title": "3.1.5", "text": "$(li)Fixes client-sided crash for the Alchemy Table when on servers. $(li)Made the Water Sigil not work in the nether (still drains LP on use)."}, {"type": "patchouli:text", "title": "3.1.4", "text": "$(li)Fixed a bug when removing max level upgrades from $(l:bloodmagic:alchemy_array/living_equipment/living_basics)Living Armour$(). $(li)Fixed a client-server desync with the $(item)item filter$() count $(li)Added an audible and visual indication for when an $(l:bloodmagic:alchemy_table/anointments)anointment$() on a $(item)tool$() runs out. $(li)Included warning flags in the $(l:bloodmagic:alchemy_table/alchemy_table#errors)Alchemy Table GUI$() that will state if there is not enough LP or if the orb is unbound/the wrong tier."}, {"type": "patchouli:text", "text": "$(li)Added the $(l:bloodmagic:alchemy_array/living_equipment/armor_upgrades/elytra)Elytra Living Armour upgrade$()! Elytra not included. $(li)Added the $(l:bloodmagic:alchemy_array/living_equipment/training_bracelet)Training Bracelet$() for the $(l:bloodmagic:alchemy_array/living_equipment/living_basics)Living Armour$()! Now you can train the upgrades you want to! $(li)Increased the durability of the $(l:bloodmagic:alchemy_array/living_equipment/living_basics)Living Armour$() to diamond level, but allowed all non-chestpiece pieces to break."}, {"type": "patchouli:text", "title": "3.1.3", "text": "$(li)The $(l:bloodmagic:utility/alchemical_reaction_chamber)Alchemical Reaction Chamber$() now will consume less durability for tools with Unbreaking. Durability is consumed at $(br)(1/(unbreakingLevel+1)) chance. $(li)Fixed fluid capabilities of the ARC. $(li)Made it so that the textbox of the $(item)Filters$() can only accept numbers. Also allowed the 'Delete' key to delete values from the text field."}, {"type": "patchouli:text", "title": "3.1.2", "text": "$(li)Reimplemented $(l:bloodmagic:demon_will/item_routing/routing_nodes)Routing Nodes$()!$(li)Added Curios support for certain items $(li)Added recipes for water and lava buckets using their respective Sigils (as well as a few other uses of Sigils inside the Alchemy Table) $(li)Fixed $(l:bloodmagic:alchemy_array/living_equipment/armor_upgrades/gift_of_ignis)Gift of Ignis$() not lowering its cooldown when not on fire."}, {"type": "patchouli:text", "text": "$(li)Allowed the $(l:bloodmagic:alchemy_array/sigil/divination)Divination Sigil$() and the $(l:bloodmagic:alchemy_array/sigil/seer)Seer Sigil's$() HUD elements to work in both the offhand and in the $(l:bloodmagic:alchemy_array/sigil/holding)Sigil of Holding$(). Also allows the $(l:bloodmagic:demon_will/will_manipulation/aura_gauge)Demon <PERSON><PERSON> Gauge$() to work in the offhand (why you'd have it in your offhand, I can't say). $(li)Added the configs! Yay! $(li)Added the Ritual '$(l:bloodmagic:rituals/ritual_list/ritual_zephyr)Call of the Zephyr$()'."}, {"type": "patchouli:text", "title": "3.1.1-16", "text": "$(li)Fixed the $(l:bloodmagic:demon_will/demonic_items/throwing_daggers)Throwing Daggers$() so that they act as the appropriate type. $(li)Fixed the $(l:bloodmagic:alchemy_array/sigil/magnetism)Sigil of Magnetism's$() range so that it is centered on the player. $(li)Fixed the $(l:bloodmagic:alchemy_array/living_equipment/living_upgrades)Living Armour upgrade$() 'Tough' so that it is now obtainable. Now you can get hurt for an actual cause! $(li)Fixed an NPE crash due to the $(l:bloodmagic:rituals/ritual_list/ritual_armour_evolve)Ritual of Living Evolution$(). Crash occurred if a player equipped a newly crafted"}, {"type": "patchouli:text", "text": "$(br)Living Armour set while on the activated ritual. $(li)Added the recipe for the $(l:bloodmagic:demon_will/will_manipulation/aura_gauge)Demon Will Aura Gauge.$() $(li)Changed the behaviour of the $(l:bloodmagic:alchemy_array/living_equipment/living_tomes)Living Upgrade Tomes$(): $(li2)$(item)Tomes$() now will attempt to apply their contained $(thing)EXP$() (not levels) regardless of current or $(item)Tome$() levels. $(li2)If $(thing)EXP$() from $(item)Tome$() would put you past the $(l:bloodmagic:alchemy_array/living_equipment/living_basics)Living Armour's$() point cap, $(thing)EXP$() is instead added up to the maximum possible for said $(thing)Living Upgrade$(), minus one."}, {"type": "patchouli:text", "text": "$(li2)$(thing)EXP$() applied to the $(item)Living Armour$() is syphoned from the $(item)Tome$(), and the remaining $(thing)EXP$() is not lost. $(li2)If the remaining $(thing)EXP$() on the $(item)Living Upgrade Tome$() is below the amount required for at least one level of its respective upgrade, the $(item)Tome$() is destroyed. $(li)Waterlogged blocks now count as \"water\" for the Incense Altar. $(li)$(l:bloodmagic:altar/blood_rune/aug_capacity_rune)Runes of Augmented Capacity$() use their previous formula (Book is accurate again)."}, {"type": "patchouli:text", "text": "$(li)Fixed duplication glitch for the $(l:bloodmagic:utility/alchemical_reaction_chamber)Alchemical Reaction Chamber$()."}, {"type": "patchouli:text", "title": "3.1.0-15", "text": "$(li)Majorly refactored the progression of the mod. Instead of starting with a $(l:bloodmagic:demon_will/will_manipulation/soul_snare)Snare$(), you instead start by crafting the $(l:bloodmagic:altar/blood_altar)Blood Altar$(). The changes are documented in the guide, and you can follow its $(l:bloodmagic:utility/getting_started)Getting Started$() entry!$(li)Changed the tooltips so that they are gray, more easily differentiating them from the name of the item."}, {"type": "patchouli:text", "text": "$(li)Added the $(l:bloodmagic:alchemy_array/holding)Sigil of Holding$().$(li)Changed the crafting of $(l:bloodmagic:demon_will/will_manipulation/soul_gem)Tartaric Gems$() so that you no longer need to use the previous tier gem in the gem slot.$(li2)The $(l:bloodmagic:demon_will/will_manipulation/soul_forge)Hellfire Forge$() will now syphon from the gem in the crafting table first, and all unused will from the consumed gem will be placed in the crafted gem."}, {"type": "patchouli:text", "text": "$(li)Changed the GUI for the $(l:bloodmagic:alchemy_table/alchemy_table)Alchemy Table$(). See the updated section.$(li)Fixed $(l:bloodmagic:alchemy_table/anointments/looting_anointment)Plunderer's Glint$() not properly applying the Looting level.$(li)Also fixed a NPE crash due to $(l:bloodmagic:alchemy_table/anointments/bow_power_anointment)Iron Tip$(). Fixes some crashes due to mods using the ItemUsedFinish event.$(li)Fixed the $(l:bloodmagic:blood_altar/blood_altar)Blood Altar$() not being able to input fluids. About time, Way!"}, {"type": "patchouli:text", "text": "$(li)Added new $(l:bloodmagic:demon_will/demonic_items/explosive_charges)Explosive Charges.$()$(li2)$(item)Fungal Charge$(), which is very useful for giant mushrooms and nether mushrooms.$(li2)$(item)Controlled Charge$(), which will destroy only blocks that match the block it is placed on.$(li)Added the ability to apply a few select Anointments to the charges. Only one can be applied to a charge at a time.$(li2)$(l:bloodmagic:alchemy_table/anointments/silk_touch_anointment)Soft Coating$()$(li2)$(l:bloodmagic:alchemy_table/anointments/fortune_anointment)Fortuna Extract$()"}, {"type": "patchouli:text", "text": "$(li2)$(l:bloodmagic:alchemy_table/anointments/smelting_anointment)Slow-burning Oil$()$(li)Fixed the $(l:bloodmagic:alchemy_array/living_equipment/living_basics)Living Armour$() so that you cannot use an upgrade tome to usurp the point cap.$(li2)Fixed $(l:bloodmagic:alchemy_array/living_equipment/armor_upgrades/strong_legs)Strong Legs$()so that it no longer runs the program \"CrunchyLegs.exe\" - as a result, you no longer suffer fall damage from jumping on the same level. Removed the direct fall damage mitigation from Strong Legs."}, {"type": "patchouli:text", "text": "$(li)Added two types of $(l:bloodmagic:demon_will/demonic_items/throwing_daggers)Throwing Daggers$() to the Blood Mage's offensive kit.$(li2)$(item)Iron Throwing Dagger$()$(li2)$(item)Syringe Throwing Dagger$()$(li)Refactored the guide so that it provides +2 to awesomeness.$()"}, {"type": "patchouli:text", "title": "3.0.6-11", "text": " $(li)Reimplemented the $(l:bloodmagic:rituals/ritual_list/ritual_well_of_suffering)Well of Suffering$(). $(li)Added several new items, called \"$(l:bloodmagic:alchemy_table/anointments)Anointments$()\"! These are items that provide temporary buffs to your tools and weapons, such as silk touch, higher damage, and more! Look them up in the Utility section of the book. These include: $(li2)Honing Oil $(li2)Soft Coating $(li2)Fortuna Extract $(li2)Holy Water"}, {"type": "patchouli:text", "text": "$(li2)Miner's Secrets $(li2)Dexterity Alkahest $(li2)Iron Tip $(li2)Plunderer's Glint $(li2)Slow-burning Oil $(li)Made the $(l:bloodmagic:demon_will/demonic_items/explosive_charges#shaped_charge)Shaped Charge$() and $(l:bloodmagic:demon_will/demonic_items/explosive_charges#deforester_charge)Deforester Charge$() throwable."}, {"type": "patchouli:text", "title": "3.0.5-10", "text": "Fixed a crash with the $(l:bloodmagic:altar/blood_altar)Blood Altar$() on unload and adjusted the recipe for the $(l:bloodmagic:alchemy_array/functional_arrays/time_arrays)Day array$() to use $(item)Coal$() instead of $(item)Clocks$()."}, {"type": "patchouli:text", "title": "3.0.4-9", "text": "$(li)Added two new $(l:bloodmagic:demon_will/demonic_items/explosive_charges)explosives$(): $(li2)The $(l:bloodmagic:demon_will/demonic_items/explosive_charges#shaped_charge)Shaped Charge$(), which will break a 5x5x5 volume when placed. $(li2)The $(l:bloodmagic:demon_will/demonic_items/explosive_charges#deforester_charge)Deforester Charge$(), which can chop down a tree up to a maximum of 128 logs. $(li)(Both explosives drop all blocks affected, and do not cause further environmental nor entity damage.)"}, {"type": "patchouli:text", "text": "$(li)Added new alchemy arrays: $(li2)$(l:bloodmagic:alchemy_array/functional_arrays/time_arrays)Two arrays$(), which changes the current daylight cycle to day and night. Recipes are temp. $(li)Fixed the following $(l:bloodmagic:alchemy_array/living_equipment/living_upgrades)Living Armour upgrades$() so that they are now obtainable: $(li2)Experienced $(li2)Body Builder"}, {"type": "patchouli:text", "title": "3.0.3-8", "text": "$(li)Updated the $(item)Sanguine Scientiem$() - it's very well written! Special thanks to both $(fire)VT-14$() and $(raw)Wrincewind$() for their long hours of effort to make this book better than I could have done! $(li)Fixed a crash with the $(l:bloodmagic:alchemy_array/living_equipment/living_basics)Living Armour$() when crafted and worn right after enchanting."}, {"type": "patchouli:text", "title": "3.0.2-7", "text": "Readded the $(l:bloodmagic:alchemy_array/living_equipment/living_basics)Living Armour$(). Currently only with a few $(l:bloodmagic:alchemy_array/living_equipment/living_upgrades)upgrades$(), and no downgrades. $(li)Pin Cushion $(li)Soft Fall $(li)Tough $(li)Strong Legs (Now can be temporarily deactivated when jumping by sneaking) $(li)Healthy $(li)Experienced $(li)Charging Strike $(li)Tough Palms $(li)Quick Feet $(li)Poison Resistance"}, {"type": "patchouli:text", "text": "$(li)Gift of Ignis $(li)Dwarven Might $(li)Body Builder $(br2)Reimplemented the following rituals: $(li)$(l:bloodmagic:rituals/ritual_list/ritual_crystal_split)Resonance of the Faceted Crystal$() $(li)$(l:bloodmagic:rituals/ritual_list/ritual_crystal_harvest)Crack of the Fractured Crystal$() $(li)$(l:bloodmagic:rituals/ritual_list/ritual_harvest)Reap of the Harvest Moon$() (Mainly vanilla crops) $(li)$(l:bloodmagic:rituals/ritual_list/ritual_animal_growth)Ritual of the Shepherd$() $(li)$(l:bloodmagic:rituals/ritual_list/ritual_green_grove)Ritual of the Green Grove$() $(li)$(l:bloodmagic:rituals/ritual_list/ritual_ellipse)Focus of the Ellipsoid$() $(li)$(l:bloodmagic:rituals/ritual_list/ritual_upgrade_remove)Sound of the Cleansing Soul$()"}, {"type": "patchouli:text", "text": "$(li)$(l:bloodmagic:rituals/ritual_list/ritual_armour_evolve)Ritual of Living Evolution$()"}, {"type": "patchouli:text", "title": "3.0.1-6", "text": "$(li)Reimplemented the $(l:bloodmagic:utility/incense_altar)Incense Altar$() with all appropriate blocks. $(li)Added guide entries for the $(l:bloodmagic:utility/incense_altar)Incense Altar$() and $(l:bloodmagic:altar/blood_altar)Blood Altar$(). Other misc entries added, too. $(li)Fixed a server-related crash on the client on the $(l:bloodmagic:alchemy_array/sigil/grove)Sigil of the Green Grove$(). $(li)Added $(l:bloodmagic:utility/alchemical_reaction_chamber#ore_processing)ore processing$() to $(item)Ancient Debris.$() $(li)Readded the other $(l:bloodmagic:demon_will/demonic_items/sentient_tools)Sentient Tool$() types (with one more on the way)."}, {"type": "patchouli:text", "text": "$(li)Added a few WIP items that cannot be used yet - they're part of the D*$)@ D*#@(@* system that I'm adding for Tier 4. $(li)Added the API - It's still in flux, so expect it to change very soon! $(li)Probably forgot something important. $(li)Fixed a crash for the $(l:bloodmagic:utility/lava_crystal)Lava Crystal$() that made it blow up the Client if used on a server. No, that was not the intended purpose!"}, {"type": "patchouli:text", "text": "$(li)Added the $(l:bloodmagic:rituals/ritual_tinkerer)Ritual Tinkerer$() for Rituals. $(li)Added the \"$(l:bloodmagic:utility/book_experience)Tome of Peritia$()\" again - stores EXP."}, {"type": "patchouli:text", "title": "3.0.0-3", "text": "$(li)Fix for an issue with the Will Handler, which would cause massive lag. $(li)Reimplemented some recipes which... were not properly implemented. These include the $(l:bloodmagic:alchemy_table/alchemy_table#cutting_fluid)Cutting Fluid$() recipes in the $(l:bloodmagic:alchemy_table/alchemy_table)Alchemy Table$(). Whoops! $(li)Reduced the cost of the $(l:bloodmagic:altar/blood_altar#blank_rune)Blank Rune$(), swapping one of the $(l:bloodmagic:altar/slates)Blank Slates$() for $(item)Smooth Stone$()."}, {"type": "patchouli:text", "title": "3.0.0-1", "text": "First update for $(item)Minecraft 1.16.3$()! Supposedly it should also work for 1.16.4, however it was built for 1.16.3. $(br2)This release is what I can best describe as \"early alpha\" - a lot of the systems are in place and working, however a few things are notably absent. This can mean that the system will either be fully revamped, reimplemented from previous MC versions at a later date, or"}, {"type": "patchouli:text", "text": "will not be added to make room for another system. $(br2)Some of the things that are currently missing from this version of Blood Magic are: $(li)$(l:bloodmagic:alchemy_array/living_equipment/living_basics)Living Armour$(), which will need to be completely rewritten to fix some client-server bugs. $(li)Several rituals. Rituals ARE in this release, but only a handful. Most of the rituals WILL be readded. $(li)The $(l:bloodmagic:utility/incense_altar)Incense Altar$(). Will be reimplemented soon."}, {"type": "patchouli:text", "text": "$(li)The Item Routing System. Might need to be reimplemented? And it'll need a bunch of insecticide, since there's some bugs that I've heard about. $(li)Tier 5 - there's not currently much in T5 from previous versions, so it's temporarily disabled until I've fully decided the upgrade path towards it. Although I won't give much away, it will involve demons. And *^$%@)$#. $(li)Tier 6 - Why do people still ask about this?"}, {"type": "patchouli:text", "text": "And other systems and items that I have not specifically mentioned. $(br2)Notable new content that's currently being experimented with and/or tweaked: $(li)The $(item)Blood Magic Guide$()! It's being completely rewritten and is currently using the mod Patchouli! $(strike)It's a pretty Neat mod by Vazkii$() It's in its early stages of writing, but for now the plan is to have it be purely informational - main reason I am temporarily ditching the narrative-based"}, {"type": "patchouli:text", "text": "guide is that I need some form of in-game guide that tells the user about all the new and improved systems in the mod. And building a comprehensive narrative makes such a thing slow moving. $(li)The $(l:bloodmagic:utility/alchemical_reaction_chamber)Alchemical Reaction Chamber$()! It's a multiple-use block that can process ores and is integral for reaching Tier 4. It's still being balanced, so any feedback on this is greatly appreciated. $(br2)I hope you will enjoy the mod! $(br)                - WayofTime"}]}