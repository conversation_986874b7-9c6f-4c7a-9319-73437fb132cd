{"name": "Throwing Daggers", "icon": "bloodmagic:throwing_dagger", "category": "bloodmagic:demon_will/demonic_items", "extra_recipe_mappings": {"bloodmagic:slate_ampoule": 3}, "pages": [{"type": "patchouli:text", "text": "$(item)Bows$() and $(item)Crossbows$() are fine enough in their way, but sifting through $(item)Gravel$() for $(item)Flint$() and plucking chickens for their $(item)Feathers$() is, frankly, beneath you. These shiny (and extremely sharp) $(item)Throwing Daggers$() also have some quite devious effects, if you do say so yourself."}, {"type": "bloodmagic:crafting_soulforge", "heading": "Iron Throwing Dagger", "recipe": "bloodmagic:soulforge/throwing_dagger", "anchor": "iron_throwing_dagger", "text": "The $(item)Iron Throwing Dagger$() is a fast-hitting attack, dealing 10 damage with a decent cooldown. Not only that, but if you have some $(l:bloodmagic:demon_will/will_manipulation/demon_will)Demon Will$() on you (be it in its raw form, or stored within a $(l:bloodmagic:demon_will/will_manipulation/soul_gem)Tartaric Gem$(), it will drop $(raw)Will$() as a $(l:bloodmagic:demon_will/demonic_items/sentient_sword)Sentient Sword$() would."}, {"type": "bloodmagic:crafting_soulforge", "heading": "<PERSON><PERSON><PERSON> Throwing <PERSON>", "recipe": "bloodmagic:soulforge/throwing_dagger_syringe", "anchor": "syringe_throwing_dagger", "text": "The $(item)Syringe Throwing Dagger$() is for the Sanguimancer more interested of the acquisition of $(blood)Life Essence$() than $(raw)Will$(). While it deals slightly less damage, it is noticeably cheaper, and enemies killed by this weapon have a chance of dropping a $(item)Slate Ampoule$() - or more, if they're hearty enough."}, {"type": "patchouli:spotlight", "item": "bloodmagic:slate_ampoule", "title": "Slate Ampoule", "text": "These delightful little vials can be crushed when near a $(l:bloodmagic:altar/blood_altar)Blood Altar$() in order to transfer $(blood)500 LP$() into it, destroying the $(item)Ampoule$() in the process. These gains are unaffected by any $(item)Runes$() you may have."}, {"type": "bloodmagic:crafting_soulforge", "heading": "<PERSON><PERSON>yst Throwing <PERSON>", "recipe": "bloodmagic:soulforge/throwing_dagger_copper", "anchor": "amethyst_throwing_dagger", "text": "The $(item)Amethyst Throwing Dagger$() does as much damage as an $(item)Iron Throwing Dagger$(), but mobs do not drop $(raw)Will$() when killed. Instead, eight of them can be crafted with a $(l:bloodmagic:alchemy_table/potions)Lingering Alchemy Flask$() in the $(item)Alchemical Reaction Chamber$() to create $(item)Tipped Amethyst Throwing Daggers$()."}, {"type": "bloodmagic:crafting_arc", "heading": "Tipped Throwing <PERSON>", "recipe": "bloodmagic:arc/tipped_copper", "text": "These will transmit their effect to any mob they hit, the same as if they'd walked into the cloud left by a $(item)Lingering Alchemy Flask$(). Experiment with combined effects to find the most debilitating, diabolical daggers you can make!"}]}