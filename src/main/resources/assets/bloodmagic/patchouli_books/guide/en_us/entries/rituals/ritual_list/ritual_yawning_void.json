{"name": "Yawning of the Void", "icon": "minecraft:black_stained_glass", "category": "bloodmagic:rituals/ritual_list", "pages": [{"type": "multiblock", "name": "Yawning of the Void", "multiblock_id": "bloodmagic:yawning_void", "text": "Use a $(l:bloodmagic:rituals/ritual_diviner#dusk)Ritual Diviner [Dusk]$(/l) for easier construction."}, {"type": "bloodmagic:ritual_info", "ritual": "yawning_void"}, {"type": "bloodmagic:ritual_data", "ritual": "yawning_void", "page_type": "raw", "text_overrides": [["Raw Will", "raw"]]}, {"type": "bloodmagic:ritual_data", "ritual": "yawning_void", "page_type": "corrosive", "text_overrides": [["filter", "item"]], "text": "To set a filter, place any form of $(item)Item Filter$() into the linked $(item)Chest$(). The ritual will only use the first filter it finds, but will accept $(l:bloodmagic:demon_will/item_routing/standard_item_filter)Standard$(), $(l:bloodmagic:demon_will/item_routing/tag_item_filter)Tag$(), $(l:bloodmagic:demon_will/item_routing/mod_item_filter)Mod$(), and $(l:bloodmagic:demon_will/item_routing/composite_item_filter)Composite Item Filters$(). Blacklisting works too!"}, {"type": "bloodmagic:ritual_data", "ritual": "yawning_void", "page_type": "steadfast"}, {"type": "bloodmagic:ritual_data", "ritual": "yawning_void", "page_type": "quarryRange"}, {"type": "bloodmagic:ritual_data", "ritual": "yawning_void", "page_type": "chest", "text_overrides": [["<PERSON><PERSON>", "item"]]}, {"type": "bloodmagic:ritual_data", "ritual": "yawning_void", "page_type": "placementRange"}]}