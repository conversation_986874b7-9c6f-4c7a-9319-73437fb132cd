{"name": "Charging <PERSON>", "icon": "bloodmagic:<PERSON><PERSON><PERSON>", "category": "bloodmagic:altar/blood_rune", "pages": [{"type": "patchouli:text", "text": "The $(item)Charging Rune$() is a unique Rune upgrade. When the $(l:bloodmagic:altar/blood_altar)Blood Altar$() is not crafting nor filling a $(l:bloodmagic:altar/soul_network)Blood Orb$(), it will syphon $(blood)LP$() from the Altar to charge an internal buffer. When an item is next placed inside of the Altar, it will instantaneously consume the stored charge and apply it to the crafting of the item at a 1:1 ratio."}, {"type": "patchouli:text", "text": "The Blood Altar does a charging tick once per 20 in-game ticks, which is reduced by 1 per $(l:bloodmagic:altar/blood_rune/acceleration_rune)Acceleration Rune.$(/l) $(br2)The speed that the Blood Altar charges at per charging tick is: [$(blood)10LP$() x $(l:bloodmagic:altar/blood_rune/charging_rune)Charging Runes$() x (1 + $(l:bloodmagic:altar/blood_rune/speed_rune)Speed Runes$()/10)] $(br2)The maximum charge that a Blood Altar can hold is $(blood)1000 LP$() per $(item)Charging Rune$(), which is then multiplied by: [(capacity of the main Blood Altar tank)/20000] if that value is above 1."}, {"type": "patchouli:crafting", "recipe": "bloodmagic:blood_rune_charging"}, {"type": "patchouli:crafting", "recipe": "bloodmagic:blood_rune_charging_2", "text": "With some $(item)Netherite Scrap$() and some $(item)Intricate Hellforged Parts$() looted from the $(l:bloodmagic:dungeons/endless_realm)Demon Realm$(), you can double the power of your $(item)Charging Rune$(), both in terms of capacity and speed."}, {"type": "bloodmagic:crafting_arc", "recipe": "bloodmagic:arc/reversion/charging", "text": "If you change your mind, you can undo the recipe in the ARC."}]}