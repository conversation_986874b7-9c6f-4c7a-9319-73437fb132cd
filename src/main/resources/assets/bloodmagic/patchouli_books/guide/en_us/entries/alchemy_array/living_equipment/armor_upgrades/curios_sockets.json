{"name": "Socketed", "icon": "bloodmagic:soulsnare", "category": "bloodmagic:alchemy_array/living_equipment/armor_upgrades", "flag": "mod:curios", "pages": [{"type": "patchouli:text", "text": "Adds $(thing)Living Armour Socket$() Curios Slots to the player that are able to hold compatible $(6)Blood Magic$() items. $(br2)This upgrade can't be trained, only crafted. By default, each $(l:bloodmagic:alchemy_array/living_equipment/living_tomes)Upgrade Tome$() adds one additional slot."}, {"type": "bloodmagic:crafting_upgrade_alchemy_table", "a.heading": "Socketed <PERSON><PERSON>", "a.recipe": "bloodmagic:alchemytable/curios_upgrade", "b.upgrade": "bloodmagic:curios_socket"}]}