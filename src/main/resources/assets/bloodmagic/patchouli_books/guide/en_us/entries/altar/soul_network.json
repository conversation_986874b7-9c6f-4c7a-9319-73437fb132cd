{"name": "Soul Network", "icon": "bloodmagic:weakb<PERSON><PERSON><PERSON>b", "category": "bloodmagic:altar", "pages": [{"type": "patchouli:text", "text": "The $(thing)Soul Network$() is the network that connects your $(thing)Soul$() to all of your bound items, rituals and blocks. Functionally, it is a global storage of $(blood)LP$() unique to each player that can be added to and extracted from, using the player's bound items as an intermediary. When you first press [$(k:use)] with an item that can be bound to a $(thing)Soul Network$(), it will bind to you and will be labeled as"}, {"type": "patchouli:text", "text": "being \"owned\" by you. As such, any action that the item does that has an $(blood)LP cost$() will drain from your $(thing)Soul Network$(). In some cases, if the item cannot get its $(blood)LP$() from the $(thing)Soul Network$(), it will instead directly take the $(blood)LP$() cost from your health.$(br2)in other cases (such as when an ongoing $(thing)Ritual$() drains your network completely), they will merely cause unceasing nausea until either the Ritual is deactivated or your $(thing)Soul Network$() is re-filled. "}, {"type": "patchouli:text", "text": "In order to fill your $(thing)Soul Network$(), you will need to construct a $(item)Blood Orb$(). $(br2)Blood Orbs can be charged with $(blood)LP$() in one of two ways. $(li)A player can sacrifice 1 heart of health by pressing [$(k:use)] with the $(item)Blood Orb$(), providing the bound $(thing)Soul Network$() with $(blood)200 LP$(). $(li)The $(item)Blood Orb$() can be placed inside a $(l:bloodmagic:altar/blood_altar)Blood Altar$(/l) with some $(blood)Life Essence$() in it. The Orb will absorb it as fast as it can, limited by your Altar's $(item)Speed Runes$()."}, {"type": "patchouli:text", "text": "There is a separate $(item)Blood Orb$() that can be created for each Tier of the $(item)Blood Altar$(). Their recipes are documented overleaf. $(li)$(item)Weak Blood Orb$() - Max capacity: $(blood)5k LP$(). $(li)$(item)Apprentice Blood Orb$() - Max capacity: $(blood)25k LP$(). $(li)$(item)Magician Blood Orb$() - Max capacity: $(blood)150k LP$(). $(li)$(item)Master Blood Orb$() - Max capacity: $(blood)1M LP$(). $(li)$(item)Archmage Blood Orb$() - Max capacity: $(blood)10M LP$()."}, {"type": "bloodmagic:2x_crafting_altar", "a.heading": "Weak Blood Orb", "a.recipe": "bloodmagic:altar/weakbloodorb", "b.heading": "Apprentice Blood Orb", "b.recipe": "bloodmagic:altar/apprenticebloodorb"}, {"type": "bloodmagic:2x_crafting_altar", "a.heading": "Magician <PERSON> Orb", "a.recipe": "bloodmagic:altar/magicianbloodorb", "b.heading": "Master Blood Orb", "b.recipe": "bloodmagic:altar/masterbloodorb"}, {"type": "bloodmagic:crafting_altar", "heading": "Archmage Blood Orb", "recipe": "bloodmagic:altar/archmageb<PERSON>odorb", "text": "If that's still not enough $(blood)LP storage$() for you, consider using $(l:bloodmagic:altar/blood_rune/orb_rune)Runes of the Orb$()."}]}