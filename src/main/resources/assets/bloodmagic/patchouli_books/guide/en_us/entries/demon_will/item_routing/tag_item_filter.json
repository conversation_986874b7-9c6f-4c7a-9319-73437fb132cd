{"name": "<PERSON>em <PERSON>", "icon": "bloodmagic:itemrouterfilteroredict", "category": "bloodmagic:demon_will/item_routing", "pages": [{"type": "patchouli:text", "text": "The $(item)Tag Item Filter$() lets you select up to 9 items and filter by their associated $(thing)Tags$(). Similarly to the $(l:bloodmagic:demon_will/item_routing/standard_item_filter)Standard Item Filter$(), it has a quantity selector and an allow/deny function. Leaving the quantity blank defaults to 'all'. $(br2)For each item that you put into this filter, you can select whether to match items based on $(thing)one specific tag$(), or $(thing)any of its tags$()."}, {"type": "bloodmagic:crafting_alchemy_table", "heading": "<PERSON>em <PERSON>", "recipe": "bloodmagic:alchemytable/tag_router_filter", "text": "This allows you to deny/permit categories of items, so you can specify that all items with the tag $(thing)forge:ores$() get sent to your furnace, for example."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/routing/tag_item_filter_gui.png", "bloodmagic:images/entries/routing/tag_item_filter_mouseover.png"], "title": "Tag Item Filter GUI", "border": true, "text": "The GUI and the mouseover text of a configured filter."}]}