{"name": "Divination Sigil", "icon": "bloodmagic:divinations<PERSON>l", "category": "bloodmagic:alchemy_array/sigil", "pages": [{"type": "patchouli:text", "text": "The $(item)Divination Sigil$() is probably the first of many sigils that you would like to craft in Blood Magic. In order to craft the sigil, you need to create an $(l:bloodmagic:alchemy_array/functional_arrays/arcane_ash)Alchemy Array$(/l) and use $(item)Redstone Dust$() and a $(item)Blank Slate$() as the base and catalyst items, respectively."}, {"type": "bloodmagic:crafting_array", "heading": "Divination Sigil", "recipe": "bloodmagic:array/divinationsigil", "text": " $(italic)Peer into the soul."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/sigil/divination_sigil.png"], "title": "Divination <PERSON><PERSON><PERSON>", "border": true, "text": "The Divination Sigil, next to its crafting array."}, {"type": "patchouli:text", "text": "The Divination Sigil has two primary uses: $(br)$(li)When any player presses [$(k:use)] while aiming at the air with a bound sigil, it will display the amount of $(blood)LP$() that is in the owner's $(l:bloodmagic:altar/soul_network)Soul Network.$(/l)$(li)When pressing [$(k:use)] on a $(l:bloodmagic:altar/blood_altar)Blood Altar$(/l), it will tell the player the altar's current Tier, the amount of $(blood)Life Essence$() stored in the altar, as well as its current max capacity. Having a $(item)Divination Sigil$() on hand can also be helpful for the $(l:bloodmagic:utility/incense_altar)Incense Altar$()."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/sigil/divination_sigil_info.png"], "title": "Divination's <PERSON><PERSON><PERSON>", "border": true, "text": "The Divination Sigil displays this HUD when you are looking at a Blood Altar."}, {"type": "patchouli:text", "text": "Finally, when holding down the sneak key and pressing [$(k:use)], a new interface opens displaying all available HUD elements from Blood Magic. An element can be selected and moved by clicking and dragging the element. Releasing the element and clicking \"Save\" will save the element's new location.$(br2)Selecting \"Default\" returns the elements to their default positions."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/sigil/gui_editing.png"], "title": "GUI Editing", "border": true, "text": "the various GUI elements of Blood Magic."}, {"type": "patchouli:text", "text": "The elements, going top to bottom, then left to right, are as follows: $(li)$(l:bloodmagic:utility/incense_altar)The Incense Altar$() (light grey)$(li)$(l:bloodmagic:alchemy_array/sigil/seer)The Seer's Sigil$() (purple)$(li)$(item)the Divination Sigil$() (lavender)$(li)The $(l:bloodmagic:demon_will/will_manipulation/aura_gauge)Demon Will Aura Gauge$() (orange) $(li)$(l:bloodmagic:alchemy_array/sigil/holding)The Sigil of Holding$() (green)"}]}