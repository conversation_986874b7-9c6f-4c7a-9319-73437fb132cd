{"name": "<PERSON><PERSON> of Peritia", "icon": "bloodmagic:experiencebook", "category": "bloodmagic:utility", "pages": [{"type": "patchouli:text", "text": "The $(item)Tome of Peritia$() allows you to safely store your $(thing)experience$(). $(br2)pressing [$(k:sneak)] and [$(k:use)] with the Tom<PERSON> in hand stores $(thing)one level of XP$(). Pressing [$(k:use)] $(thing)retrieves a level$(). Hold [$(k:use)] to $(thing)store/retrieve multiple levels$()."}, {"type": "patchouli:crafting", "recipe": "bloodmagic:experience_tome"}, {"type": "patchouli:text", "flag": "mod:curios", "text": "As you have the $(thing)Curios API$() installed, you can equip the $(item)Tome of Peritia$() as a charm. If you want to wear more curios at once, consider using a $(l:bloodmagic:alchemy_array/sigil/holding)Sigil of Holding$(), or the $(l:bloodmagic:alchemy_array/living_equipment/armor_upgrades/curios_sockets)Socketed Upgrade$() for your $(l:bloodmagic:alchemy_array/living_equipment/living_basics)Living Armour$()."}]}