{"name": "Sigil of the Green Grove", "icon": "bloodmagic:growthsigil", "category": "bloodmagic:alchemy_array/sigil", "pages": [{"type": "patchouli:text", "text": "The $(item)Sigil of the Green Grove$() is an item that has multiple uses. Crafted in an array with a $(item)Growth Reagent$() and a $(item)Reinforced Slate$(), the sigil can use your $(thing)Soul Network$()'s $(blood)LP$() to nourish and grow nearby plants."}, {"type": "bloodmagic:crafting_2-step_sigil", "alchemy_table.heading": "Growth Reagent", "alchemy_table.recipe": "bloodmagic:alchemytable/reagent_growth", "array.heading": "Sigil of the Green Grove", "array.recipe": "bloodmagic:array/growthsigil"}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/sigil/grove_sigil1.png", "bloodmagic:images/entries/sigil/grove_sigil2.png", "bloodmagic:images/entries/sigil/grove_sigil3.png"], "title": "Green Grove Sigil Array", "border": true, "text": "The Sigil of the Green Grove's array, plus its primary uses."}, {"type": "patchouli:text", "text": "If you press [$(k:use)] on a block that is $(2)IGrowable$(), it will apply the bonemeal effect while consuming $(blood)150 LP$(). $(br2)However, if you hold [$(k:sneak)] and [$(k:use)] while aiming at the air, it will light up to indicate that it is activated, and will consume $(blood)150 LP$() every 5 seconds until deactivated. Every block in a 7x7x5 high volume centered on the player will have a growth tick applied to it. Good for farming those taters!"}, {"type": "patchouli:text", "flag": "mod:curios", "text": "As you have the $(thing)Curios API$() installed, you can equip this $(item)Sigil$() as a charm. If you want to wear more curios at once, consider using a $(l:bloodmagic:alchemy_array/sigil/holding)Sigil of Holding$(), or the $(l:bloodmagic:alchemy_array/living_equipment/armor_upgrades/curios_sockets)Socketed Upgrade$() for your $(l:bloodmagic:alchemy_array/living_equipment/living_basics)Living Armour$()."}]}