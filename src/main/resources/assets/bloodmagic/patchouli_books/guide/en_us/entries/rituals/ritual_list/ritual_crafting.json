{"name": "Rhythm of the Beating Anvil", "icon": "minecraft:crafting_table", "category": "bloodmagic:rituals/ritual_list", "pages": [{"type": "multiblock", "name": "Rhythm of the Beating Anvil", "multiblock_id": "bloodmagic:crafting", "text": "Use a $(l:bloodmagic:rituals/ritual_diviner#dusk)Ritual Diviner [Dusk]$(/l) for easier construction."}, {"type": "bloodmagic:ritual_info", "ritual": "crafting", "text_overrides": [["<PERSON><PERSON>", "l:bloodmagic:demon_will/item_routing/standard_item_filter"]]}, {"type": "patchouli:text", "text": "The $(thing)Rythm of the Beating Anvil$() is a powerful and versatile ritual, allowing you to autocraft standard crafting recipes alongside recipes using your $(l:bloodmagic:alchemy_table/alchemy_table)Alchemy Table$() or $(l:bloodmagic:demon_will/will_manipulation/soul_forge)Hellfire Forge$() (If properly augmented). However, it can be a little complex, so what follows is a tutorial for setting it up. $(br2)Each ritual can handle exactly one recipe."}, {"type": "patchouli:text", "text": "First off, we need to specify the recipe that we want the ritual to craft. This is done using an Item Filter. $(br2)Multiple types of Item Filter can be used, however the ritual will only ever accept one filter at a time; any additional filters will be ignored."}, {"type": "patchouli:text", "text": "The following filters are accepted: $(li)Standard Item Filter: specifies exactly which item to use in each slot. $(li)Tag Item Filter: Uses $(thing)Tags$() to specify what items to use. E.G. If a recipe requires any kind of stone (but will work with andesite, granite, etc), you can use forge:stone in the filter. $(li)Mod Item Filter: This will try to use any item from the mod in this slot."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/ritual/autocraft/filter_pickaxe.png"], "title": "<PERSON><PERSON> Filter", "border": true, "text": "A Standard Item Filter configured to autocraft $(item)Stone Pickaxes$()."}, {"type": "patchouli:text", "text": "Next, the Item Filter has to be placed on the ritual. In this image we've used an $(item)Item Frame$(), however a chest can be used instead. Note that if you put multiple $(item)filters$() in the chest, only the first one will be used. $(br2)By default, the Input chest and Output chest are in the same place; however, this can be changed with the $(l:bloodmagic/rituals/ritual_tinkerer)Ritual Tinkerer.$() This can be useful for recipes that re-use some components, such as your $(l:bloodmagic:altar/soul_network)Blood Orb.$()"}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/ritual/autocraft/default_placement.png", "bloodmagic:images/entries/ritual/autocraft/optional_placement.png"], "title": "The Placed Filter", "border": true, "text": "A filter and chest in normal locations, and a $(l:bloodmagic/rituals/ritual_tinkerer)Tinkered$() ritual with in and out chests"}, {"type": "patchouli:text", "text": "If you supply the ritual with $(steadfast)Steadfast$() or $(corrosive)Corrosive$() Will, then the ritual will instead try to autocraft with a linked $(l:bloodmagic:demon_will/will_manipulation/soul_forge)Hellfire Forge$() or $(l:bloodmagic:alchemy_table/alchemy_table)Alchemy Table$(), respectively. These recipes are all shapeless, but if you need 2 of an item, you'll have to specify it twice in the filter."}, {"type": "patchouli:image", "images": ["bloodmagic:images/entries/ritual/autocraft/catalyst_recipe.png"], "title": "Alchemy Filter", "border": true, "text": "A filter configured for the crafting of $(l:bloodmagic:alchemy_table/potions)Simple Catalysts$()."}, {"type": "bloodmagic:ritual_data", "ritual": "crafting", "page_type": "recipeRange", "text_overrides": [["<PERSON><PERSON>", "l:bloodmagic:demon_will/item_routing/standard_item_filter"]], "text": "Supports $(l:bloodmagic:demon_will/item_routing/standard_item_filter)Standard$(), $(l:bloodmagic:demon_will/item_routing/tag_item_filter)Tag$(), and $(l:bloodmagic:demon_will/item_routing/mod_item_filter)Mod Item Filters$()."}, {"type": "bloodmagic:ritual_data", "ritual": "crafting", "page_type": "inputRange"}, {"type": "bloodmagic:ritual_data", "ritual": "crafting", "page_type": "outputRange"}, {"type": "bloodmagic:ritual_data", "ritual": "crafting", "page_type": "outputFilterRange", "text_overrides": [["filter", "l:bloodmagic:demon_will/item_routing/standard_item_filter"]], "text": "Supports $(l:bloodmagic:demon_will/item_routing/standard_item_filter)Standard$(), $(l:bloodmagic:demon_will/item_routing/tag_item_filter)Tag$(), and $(l:bloodmagic:demon_will/item_routing/mod_item_filter)Mod Item Filters$()."}, {"type": "bloodmagic:ritual_data", "ritual": "crafting", "page_type": "hellforgedRange"}, {"type": "bloodmagic:ritual_data", "ritual": "crafting", "page_type": "steadfast"}, {"type": "bloodmagic:ritual_data", "ritual": "crafting", "page_type": "corrosive"}]}