{"name": "Tiers & Getting Started", "icon": "bloodmagic:sacrificial<PERSON>gger", "category": "bloodmagic:utility", "priority": "true", "pages": [{"type": "patchouli:text", "text": "$(blood)Blood Magic 3$()'s progression is still being reworked, and the first few steps are significantly different from $(blood)Blood Magic 2$()'s. $(br2)We are still retooling our guidance, but we are waiting until the progression is locked down. In the meantime, here is a quick overview on how to progress in $(blood)Blood Magic 3$()."}, {"type": "patchouli:spotlight", "item": "bloodmagic:altar", "title": "Blood Altar (Tier-1)", "text": "The first step of $(blood)Blood Magic$() is to build a $(l:bloodmagic:altar/blood_altar)Blood Altar$() and $(l:bloodmagic:altar/blood_altar#knife)Sacrificial Knife$(). Use these to generate $(blood)LP$() from Self-Sacrificing.$(br2)Use this $(blood)LP$() to craft a $(l:bloodmagic:altar/soul_network)Weak Blood Orb$(), several $(l:bloodmagic:altar/slates)Blank Slates$(), and a few $(l:bloodmagic:demon_will/will_manipulation/soul_snare)Soul Snares$()."}, {"type": "patchouli:spotlight", "item": "bloodmagic:alchemytable", "title": "Alchemy Table (Tier-1)", "text": "The $(l:bloodmagic:alchemy_table/alchemy_table)Alchemy Table$() uses $(blood)LP$() from a player's $(thing)Soul Network$() (drawn from the $(l:bloodmagic:altar/soul_network)bound Blood Orb$() in it) to craft various different objects, such as:$(li)$(l:bloodmagic:alchemy_array/functional_arrays/arcane_ash)Arcane Ashes$()$(li)$(item)Reagents for Sigils$()$(li)$(l:bloodmagic:alchemy_table/anointments)Anointments$()$(li)$(thing)2x Ore Processing$()$(li)and various other odds and ends."}, {"type": "patchouli:spotlight", "item": "bloodmagic:arcaneashes", "title": "Alchemy Array (Tier-1)", "text": "An $(thing)Alchemy Array$() is made by placing some $(l:bloodmagic:alchemy_array/functional_arrays/arcane_ash)Arcane Ashes$() on the ground. The Alchemy Array can have 2 $(item)items$() inserted into it via [$(k:use)], and will either craft an $(item)item$() (such as a $(l:bloodmagic:alchemy_array/sigil/divination)Divination Sigil$()) or perform $(thing)some kind of function$() (such as $(l:bloodmagic:alchemy_array/functional_arrays/time_arrays)turning day into night$())."}, {"type": "patchouli:spotlight", "item": "bloodmagic:soulforge", "title": "Hellfire Forge (Tier-1)", "text": "The $(l:bloodmagic:demon_will/will_manipulation/soul_forge)Hellfire Forge$() crafts using $(l:bloodmagic:demon_will/will_manipulation/demon_will)Demon Will$(). You get your first $(raw)Will$() by using $(l:bloodmagic:demon_will/will_manipulation/soul_snare)Soul Snares$(), though upgrading to a $(l:bloodmagic:demon_will/demonic_items/sentient_sword)Sentient Sword$() is recommended. The $(item)Hellfire Forge$() is used for stuff directly related to $(raw)Demon Will$() (like $(l:bloodmagic:demon_will/will_manipulation/soul_gem)Tartaric Gems$() and $(l:bloodmagic:demon_will/demonic_items/sentient_tools)Sentient Tools$()), and consumables (like $(l:bloodmagic:demon_will/demonic_items/explosive_charges)Explosive Charges$() and $(l:bloodmagic:demon_will/demonic_items/throwing_daggers)Throwing Daggers$())."}, {"type": "patchouli:spotlight", "item": "bloodmagic:daggerofsacrifice", "title": "Tier Two", "text": "At this point, you will be able to craft a $(l:bloodmagic:altar/blood_altar#dagger_of_sacrifice)Da<PERSON> of Sacrifice$() in order to slaughter mobs for more $(blood)LP$(). Various $(item)Upgrade Runes$() should be available for your $(item)Blood Altar$(), and some more $(item)Sigils$() will be available. As before, you should focus on further upgrading your $(l:bloodmagic:altar/blood_altar)Altar$()."}, {"type": "patchouli:spotlight", "item": "bloodmagic:alchemy_flask", "title": "Potioncrafting (Tier-2)", "text": "If building your $(l:bloodmagic:altar/blood_altar)Altar$() is getting tiresome and you feel in need of a distraction, why not try out the newly available $(l:bloodmagic:alchemy_table/potions)Potioncrafting$() system? you will be able to craft $(thing)Alchemy Flasks$() or $(l:bloodmagic:demon_will/demonic_items/throwing_daggers#amethyst_throwing_dagger)Tipped Amethyst Throwing Daggers$() and imbue them with literally dozens of effects."}, {"type": "patchouli:spotlight", "item": "bloodmagic:ritual<PERSON><PERSON>er", "title": "Tier Three", "text": "By this point, you will have the ability to create some basic $(l:bloodmagic:rituals/ritual_basics)Rituals$() and $(l:bloodmagic:alchemy_array/living_equipment/living_basics)Living Armour$() as well. This armour is very versatile, though you'll have to work hard to unlock its full potential. At this point you should look into upgrading your $(l:bloodmagic:altar/blood_altar)Altar$() and your $(l:bloodmagic:rituals/ritual_diviner)Ritual Diviner$() to unlock more powerful $(thing)Rituals$(). How, you say?"}, {"type": "patchouli:spotlight", "item": "bloodmagic:simplekey", "title": "Dungeoneering (Tier-3)", "text": "By performing the $(l:bloodmagic:rituals/ritual_list/ritual_simple_dungeon)Edge of the Hidden Realm$(), that's how! This will allow you limited access to $(thing)Demon Realm$(), and hopefully to $(l:bloodmagic:dungeons/tau_fruit)Tau Fruit$(), which can be cultivated into $(thing)Saturated Tau$() and then converted into $(item)Weak Blood Shards$() in the $(l:bloodmagic:utility/alchemical_reaction_chamber)Alchemical Reaction Chamber$(). These can be used to make the Tier IV Altar, more powerful $(l:bloodmagic:alchemy_table/anointments)Anointments$(), and $(l:bloodmagic:alchemy_table/potions#average_potions)Potion Catalysts$()."}, {"type": "patchouli:spotlight", "item": "bloodmagic:duskscribetool", "title": "Tier Four", "text": "at Tier 4, you will gain access to the $(l:bloodmagic:rituals/ritual_diviner#dusk)Ritual Diviner [Dusk]$(/l), and with it, a plethora of more advanced $(thing)Rituals$(), allowing such feats as $(l:bloodmagic:rituals/ritual_list/ritual_well_of_suffering)automating your LP supply$(), unlimited $(l:bloodmagic:rituals/ritual_list/ritual_condor)creative flight within your base$(), and even $(l:bloodmagic:rituals/ritual_list/ritual_meteor)summoning a devastating meteor$() from the heavens, chock full of goodies!"}, {"type": "patchouli:spotlight", "item": "bloodmagic:<PERSON>tome", "title": "Better Armour (Tier-4)", "text": "You may have tinkered around with $(l:bloodmagic:alchemy_array/living_equipment/living_basics)Living Armour$() by this point, but the relatively low cap of points may have started to chafe. With the $(l:bloodmagic:rituals/ritual_diviner#dusk)Ritual Diviner [Dusk]$(/l), you are now free to augment your armour like never before. Firstly, The $(l:bloodmagic:rituals/ritual_list/ritual_armour_evolve)Ritual of Living Evolution$() will allow you to raise your points cap from 100 to 300."}, {"type": "patchouli:text", "text": "If that's not enough, you can use the $(l:bloodmagic:rituals/ritual_list/ritual_upgrade_remove)Sound of the Cleansing Soul$() to strip your armour of its upgrades. These tomes can be copied into a $(l:bloodmagic:alchemy_array/living_equipment/training_bracelet)Training Bracelet$(), or re-applied to your armour to ensure you only train Upgrades that you actually want. $(br2)Any leftover $(thing)Upgrade Tomes$() can be kept as fuel for the $(l:bloodmagic:rituals/ritual_list/ritual_living_downgrade)Penance of the Leaden Soul$(). This ritual allows you to apply $(thing)Downgrades$() to your Armour."}, {"type": "patchouli:text", "text": "While these are expensive (and each comes with a hefty penalty), their negative point-cost will give you more room to further improve your upgrades. $(br2)Want to be incredibly tough and healthy, and don't mind being slow? Try combining $(l:bloodmagic:alchemy_array/living_equipment/armor_upgrades/body_builder)Body Builder$() V, $(l:bloodmagic:alchemy_array/living_equipment/armor_upgrades/brilliance)Brilliance$() V, $(l:bloodmagic:alchemy_array/living_equipment/armor_upgrades/healthy)Healthy$() X, and $(l:bloodmagic:alchemy_array/living_equipment/armor_upgrades/tough)Tough$() X with $(l:bloodmagic:alchemy_array/living_equipment/armour_downgrades/dig_slowdown)Leadened Pick$() X and $(l:bloodmagic:alchemy_array/living_equipment/armour_downgrades/speed_decreas e)Limp Leg$() X."}, {"type": "patchouli:text", "text": "Feel more like exploring and going fast? Perhaps you don't care about your offhand? Maybe $(l:bloodmagic:alchemy_array/living_equipment/armor_upgrades/strong_legs)Strong Legs$() X, $(l:bloodmagic:alchemy_array/living_equipment/armor_upgrades/quick_feet)Quick Feet$() X, and $(l:bloodmagic:alchemy_array/living_equipment/armour_downgrades/crippled_arm)Crippled Arm$() would be more your speed. $(br2)Feel like having both at different times? Make multiple chestplates and swap between them! There are dozens of upgrades and downgrades, so mix and match to find your favourite combinations."}, {"type": "patchouli:spotlight", "item": "bloodmagic:vengefulcrystal", "title": "Aspected Will (Tier-4)", "text": "You may want to look into the $(l:bloodmagic:rituals/ritual_tinkerer)Ritual Tinkerer$() and the various kinds of $(l:bloodmagic:demon_will/will_manipulation/aspected_will)Will Aspects$() available to you, and consider how they may be used to refine your existing rituals and alter how your $(l:bloodmagic:demon_will/demonic_items/sentient_tools)Sentient Tools$() and $(l:bloodmagic:demon_will/demonic_items/sentient_sword)Weapons$() work. To progress beyond this point, however, a delve into the $(thing)Demon Realm$() will be needed..."}, {"type": "patchouli:spotlight", "item": "bloodmagic:<PERSON><PERSON><PERSON><PERSON>", "title": "The Demon Realm (Tier-4)", "text": "By performing the $(l:bloodmagic:rituals/ritual_list/ritual_standard_dungeon)Pathway to the Endless Realm$(), you can gain access to the $(thing)Demon Realm$() proper, along with all of its treasures and terrors. Come equipped for a fight! Delve deep, and you may find a rich source of $(l:bloodmagic:dungeons/demonite)Demonite Ore$(), which can be combined into block form and used for the capstones on your altar, along with the $(item)Archmage's Blood Orb$()."}, {"type": "patchouli:spotlight", "item": "bloodmagic:ingot_hellforged", "title": "Tier Five", "text": "This is the top tier. At this point you have access to everything that $(blood)Blood Magic$() has to offer. Continue to delve into the Demon Realm to hunt for rare treasures, and you may be lucky enough to find $(item)Intricate Hellforged Parts$(), which can be used alongside some $(item)Netherite Scrap$() to double the power of each of your altar's Runes!"}, {"type": "patchouli:spotlight", "item": "minecraft:barrier", "title": "Tier Six?", "text": "What? No. Look, even in 1.12, this only existed as a favor to pack makers and was otherwise unimplemented. You've come too far. Go back a page."}]}