{"name": "Ore Processing", "icon": "bloodmagic:basiccuttingfluid", "category": "bloodmagic:utility", "extra_recipe_mappings": {"bloodmagic:copperfragment": 7, "bloodmagic:goldfragment": 7, "bloodmagic:fragment_netherite_scrap": 7, "bloodmagic:coppergravel": 9, "bloodmagic:goldgravel": 9, "bloodmagic:gravel_netherite_scrap": 9, "bloodmagic:coppersand": 7, "bloodmagic:sand_netherite": 7}, "pages": [{"type": "patchouli:text", "text": "The $(l:bloodmagic:alchemy_table/alchemy_table)Alchemy Table$() can be used, amongst other things, for ore doubling, whilst the $(l:bloodmagic:utility/alchemical_reaction_chamber)Alchemical Reaction Chamber$() can give you 2.5 ingots per piece of $(item)Raw Ore$(), or 4.5 ingots per $(item)Ore Block$(). Get more ore out of your mining expeditions with the power of blood!"}, {"type": "bloodmagic:crafting_alchemy_table", "heading": "Basic Cutting Fluid", "anchor": "cutting_fluid", "recipe": "bloodmagic:alchemytable/basic_cutting_fluid_sigil", "text": "$(item)Cutting Fluid$() is the penultimate step in all forms of Ore Processing. It can be used in the $(l:bloodmagic:utility/alchemical_reaction_chamber)Alchemical Reaction Chamber$(/l) to get 3 Ore Sand from one Ore Block, or 1.5 Ore Sand from one Raw Ore (on average). While a $(l:bloodmagic:alchemy_array/sigil/water)Water Sigil$() is used in this demonstration, a simple $(water)Bottle of Water$() may be used."}, {"type": "bloodmagic:crafting_alchemy_table", "heading": "Intermediate Cutting Fluid", "anchor": "intermediate_cutting_fluid", "recipe": "bloodmagic:alchemytable/intermediate_cutting_fluid_sigil", "text": "Intermediate Cutting Fluid is an improved version that lasts eight times as long and increases crafting speed by 50%. You'll have to go $(l:bloodmagic:dungeons/lobby)Dungeon Delving$() for the $(l:bloodmagic:dungeons/tau_fruit)Tau Oil$(), though."}, {"type": "bloodmagic:crafting_alchemy_table", "heading": "Advanced Cutting Fluid", "anchor": "advanced_cutting_fluid", "recipe": "bloodmagic:alchemytable/advance_cutting_fluid_sigil", "text": "Advanced Cutting Fluid lasts sixteen times as long, doubles crafting speed, and doubles the chance of getting bonus outputs from any recipes it's used in. You'll have to do some deep $(l:bloodmagic:dungeons/endless_realm)Dungeon Delving$() for the $(l:bloodmagic:dungeons/demonite)Hellforged Sand$() it needs."}, {"type": "bloodmagic:2x_crafting_alchemy_table", "a.heading": "Iron Sand", "a.recipe": "bloodmagic:alchemytable/sand_iron", "b.heading": "Gold Sand", "b.recipe": "bloodmagic:alchemytable/sand_gold"}, {"type": "bloodmagic:crafting_arc", "heading": "Ore to 3 Metal Sand", "recipe": "bloodmagic:arc/dustsfrom_ore_iron", "text": "Once you have access to the $(l:bloodmagic:utility/alchemical_reaction_chamber)Alchemical Reaction Chamber$(/l), you can get 3 sand from every ore you mine."}, {"type": "patchouli:smelting", "recipe": "bloodmagic:smelting/ingot_iron", "recipe2": "bloodmagic:smelting/ingot_gold"}, {"type": "bloodmagic:crafting_alchemy_table", "heading": "Explosive Powder", "anchor": "ore_processing", "recipe": "bloodmagic:alchemytable/explosive_powder", "text": "$(item)Explosive Powder$() in the $(item)ARC$() is used to turn $(item)Ores$() into 4.5 $(item)Ore Fragments$() on average, or $(item)Raw Ores$() into 2.25 ores on average, or turn $(item)Ingots$() into their $(item)Sand$() variant. It can also turn $(fire)Netherrack$() into $(air)Sulfur$() and 50mb of $(fire)Lava$(). It has 2 improved variants, overleaf."}, {"type": "bloodmagic:2x_crafting_alchemy_table", "a.heading": "Reinforced Explosive Cell", "a.recipe": "bloodmagic:alchemytable/explosive_cell", "b.heading": "Hellforged Explosive Cell", "b.recipe": "bloodmagic:alchemytable/hellforged_explosive_cell", "text": "the Reinforced and Hellforged Explosive Cells last longer and craft faster than the basic variant."}, {"type": "bloodmagic:3x_crafting_arc", "a.heading": "Raw Ore to Fragments", "a.recipe": "bloodmagic:arc/fragmentsiron", "b.heading": "Ore to Fragments", "b.recipe": "bloodmagic:arc/fragmentsfrom_ore_iron", "c.heading": "Sulfur and Lava", "c.recipe": "bloodmagic:arc/netherrack_to_sulfer", "c.fluid_output": "minecraft:lava_bucket"}, {"type": "bloodmagic:crafting_soulforge", "heading": "Resonator", "recipe": "bloodmagic:soulforge/resonator", "text": "The $(item)Resonator$() is used to turn $(item)Ore Fragments$() into the relevant $(item)Gravel$() for continued ore processing, and creates $(item)Tiny Corrupted Dust$(). The Reinforced Resonator has 4x durability, and the Hellforged resonator has 16x durability and doubles any bonus outputs."}, {"type": "bloodmagic:2x_crafting_soulforge", "a.heading": "Reinforced Resonator", "a.recipe": "bloodmagic:soulforge/primitive_resonator", "b.heading": "Hellforged Resonator", "b.recipe": "bloodmagic:soulforge/hellforged_resonator"}, {"type": "bloodmagic:2x_crafting_arc", "a.heading": "Fragment to Gravel", "a.recipe": "bloodmagic:arc/gravelsiron", "b.heading": "Gravel to Sand", "b.recipe": "bloodmagic:arc/dustsfrom_gravel_iron"}, {"type": "patchouli:crafting", "heading": "Corrupted Dust", "recipe": "bloodmagic:corrupted_dust", "text": "Tiny Corrupted dust can be combined into $(item)Corrupted Dust$(), which can be used to further boost the yield of other ores. See overleaf for some examples."}, {"type": "bloodmagic:3x_crafting_alchemy_table", "a.heading": "Corrupted Coal", "a.recipe": "bloodmagic:alchemytable/corrupted_coal", "b.heading": "Corrupted Iron", "b.recipe": "bloodmagic:alchemytable/corrupted_iron", "c.heading": "Corrupted Debris", "c.recipe": "bloodmagic:alchemytable/corrupted_netherite"}, {"type": "patchouli:crafting", "heading": "Fuel Cell (Furnace)", "recipe": "bloodmagic:primitive_furnace_cell", "text": "The $(item)ARC$() also functions as a $(thing)Furnace$(), but the only fuel sources it accepts is the $(item)Primitive Fuel Cell$() or a $(l:bloodmagic:utility/lava_crystal)Lava Crystal$(/l)."}, {"type": "patchouli:text", "text": "The $(item)Primitive Fuel Cell$() is good for 128 individual uses. That's more than the $(item)Block of Coal$() used to craft it (60 items), and since it only loses durability when the crafting is finished it will not waste fuel."}]}