package wayoftime.bloodmagic.recipe.helper;

import java.util.List;
import java.util.function.Predicate;

import javax.annotation.Nonnull;

import com.google.gson.JsonElement;

import net.minecraft.network.FriendlyByteBuf;

public interface InputIngredient<TYPE> extends Predicate<TYPE>
{

	/**
	 * Evaluates this predicate on the given argument, ignoring any size data.
	 *
	 * @param type the input argument
	 *
	 * @return {@code true} if the input argument matches the predicate, otherwise
	 *         {@code false}
	 */
	boolean testType(@Nonnull TYPE type);

	TYPE getMatchingInstance(TYPE type);

	/**
	 * Primarily for JEI, a list of valid instances of the type
	 *
	 * @return List (empty means no valid registrations found and recipe is to be
	 *         hidden)
	 *
	 * @apiNote Do not modify any of the values returned by the representations
	 */
	@Nonnull
	List<TYPE> getRepresentations();

	/**
	 * Writes this ingredient to a PacketBuffer.
	 *
	 * @param buffer The buffer to write to.
	 */
	void write(FriendlyByteBuf buffer);

	/**
	 * Serializes this ingredient to a JsonElement
	 *
	 * @return JsonElement representation of this ingredient.
	 */
	@Nonnull
	JsonElement serialize();
}