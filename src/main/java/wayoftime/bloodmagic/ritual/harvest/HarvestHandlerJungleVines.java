package wayoftime.bloodmagic.ritual.harvest;

import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.storage.loot.LootParams;
import net.minecraft.world.level.storage.loot.parameters.LootContextParams;
import net.minecraft.world.phys.Vec3;

import java.util.List;

public class HarvestHandlerJungleVines implements IHarvestHandler {

    private static final ItemStack mockShear = new ItemStack(Items.SHEARS, 1);

    @Override
    public boolean harvest(Level world, BlockPos pos, BlockState state, List<ItemStack> drops) {
        LootParams.Builder lootBuilder = new LootParams.Builder((ServerLevel) world);
        Vec3 blockCenter = new Vec3(pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5);
        List<ItemStack> blockDrops = state.getDrops(lootBuilder.withParameter(LootContextParams.ORIGIN, blockCenter).withParameter(LootContextParams.TOOL, mockShear));
        drops.addAll(blockDrops);
        world.destroyBlock(pos, false);
        return true;
    }

    @Override
    public boolean test(Level world, BlockPos pos, BlockState state) {
         return (state.is(Blocks.VINE) && world.getBlockState(pos.above()).is(Blocks.VINE));
    }
}
