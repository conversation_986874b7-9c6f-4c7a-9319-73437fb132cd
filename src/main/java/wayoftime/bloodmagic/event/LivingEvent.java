package wayoftime.bloodmagic.event;

import net.minecraftforge.eventbus.api.Event;

public class LivingEvent extends Event
{

//	public static final Event<Jump> JUMP = EventFactory.createArrayBacked(Jump.class, handlers -> e -> {
//		for (Jump handler : handlers) if (handler.onJump(e) == EventResult.CANCEL)
//			return EventResult.CANCEL;
//
//		return EventResult.PASS;
//	});
//	public static final Event<Damage> DAMAGE = EventFactory.createArrayBacked(Damage.class, handlers -> (e, s, d) -> {
//		for (Damage handler : handlers) if (handler.onDamage(e, s, d) == EventResult.CANCEL)
//			return EventResult.CANCEL;
//
//		return EventResult.PASS;
//	});
//
//	public interface Jump
//	{
//		EventResult onJump(LivingEntity livingEntity);
//	}
//
//	public interface Damage
//	{
//		EventResult onDamage(LivingEntity livingEntity, DamageSource source, Value<Float> damage);
//	}
}
