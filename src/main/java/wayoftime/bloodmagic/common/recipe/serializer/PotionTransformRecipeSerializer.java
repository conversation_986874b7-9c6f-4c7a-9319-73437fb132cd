package wayoftime.bloodmagic.common.recipe.serializer;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Nonnull;

import org.apache.commons.lang3.tuple.Pair;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import net.minecraft.world.item.crafting.RecipeSerializer;
import net.minecraft.world.item.crafting.Ingredient;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.util.GsonHelper;
import net.minecraft.resources.ResourceLocation;
import wayoftime.bloodmagic.potion.BloodMagicPotions;
import wayoftime.bloodmagic.recipe.flask.RecipePotionEffect;
import wayoftime.bloodmagic.recipe.flask.RecipePotionTransform;
import wayoftime.bloodmagic.util.Constants;

public class PotionTransformRecipeSerializer<RECIPE extends RecipePotionTransform>  implements RecipeSerializer<RECIPE>
{
	private final IFactory<RECIPE> factory;

	public PotionTransformRecipeSerializer(IFactory<RECIPE> factory)
	{
		this.factory = factory;
	}

	@Nonnull
	@Override
	public RECIPE fromJson(@Nonnull ResourceLocation recipeId, @Nonnull JsonObject json)
	{
		List<Ingredient> inputList = new ArrayList<Ingredient>();

		if (json.has(Constants.JSON.INPUT) && GsonHelper.isArrayNode(json, Constants.JSON.INPUT))
		{
			JsonArray mainArray = GsonHelper.getAsJsonArray(json, Constants.JSON.INPUT);

			arrayLoop: for (JsonElement element : mainArray)
			{
				if (inputList.size() >= RecipePotionEffect.MAX_INPUTS)
				{
					break arrayLoop;
				}

				if (element.isJsonArray())
				{
					element = element.getAsJsonArray();
				} else
				{
					element.getAsJsonObject();
				}

				inputList.add(Ingredient.fromJson(element));
			}
		}

		List<Pair<MobEffect, Integer>> outputEffectList = new ArrayList<>();
		if (json.has(Constants.JSON.OUTPUT_EFFECT) && GsonHelper.isArrayNode(json, Constants.JSON.OUTPUT_EFFECT))
		{
			JsonArray mainArray = GsonHelper.getAsJsonArray(json, Constants.JSON.OUTPUT_EFFECT);

			for (JsonElement element : mainArray)
			{
				JsonObject obj = element.getAsJsonObject();
				MobEffect outputEffect = BloodMagicPotions.getEffect(new ResourceLocation(GsonHelper.getAsString(obj, Constants.JSON.EFFECT)));
				int baseDuration = GsonHelper.getAsInt(obj, Constants.JSON.DURATION);

				outputEffectList.add(Pair.of(outputEffect, baseDuration));
			}
		}

		List<MobEffect> inputEffectList = new ArrayList<>();
		if (json.has(Constants.JSON.INPUT_EFFECT) && GsonHelper.isArrayNode(json, Constants.JSON.INPUT_EFFECT))
		{
			JsonArray mainArray = GsonHelper.getAsJsonArray(json, Constants.JSON.INPUT_EFFECT);

			for (JsonElement element : mainArray)
			{
				MobEffect inputEffect = BloodMagicPotions.getEffect(new ResourceLocation(GsonHelper.convertToString(element, Constants.JSON.EFFECT)));

				inputEffectList.add(inputEffect);
			}
		}

		int syphon = GsonHelper.getAsInt(json, Constants.JSON.SYPHON);
		int ticks = GsonHelper.getAsInt(json, Constants.JSON.TICKS);
		int minimumTier = GsonHelper.getAsInt(json, Constants.JSON.ALTAR_TIER);

		return this.factory.create(recipeId, inputList, outputEffectList, inputEffectList, syphon, ticks, minimumTier);
	}

	@Override
	public RECIPE fromNetwork(@Nonnull ResourceLocation recipeId, @Nonnull FriendlyByteBuf buffer)
	{
		try
		{
			int size = buffer.readInt();
			List<Ingredient> input = new ArrayList<Ingredient>(size);

			for (int i = 0; i < size; i++)
			{
				input.add(i, Ingredient.fromNetwork(buffer));
			}

			int syphon = buffer.readInt();
			int ticks = buffer.readInt();
			int minimumTier = buffer.readInt();

			int outputEffectSize = buffer.readInt();
			List<Pair<MobEffect, Integer>> outputEffectList = new ArrayList<>(outputEffectSize);

			for (int i = 0; i < outputEffectSize; i++)
			{
				int effectId = buffer.readInt();
				outputEffectList.add(i, Pair.of(MobEffect.byId(effectId), buffer.readInt()));
			}

			int inputEffectSize = buffer.readInt();
			List<MobEffect> inputEffectList = new ArrayList<>();

			for (int i = 0; i < inputEffectSize; i++)
			{
				inputEffectList.add(i, MobEffect.byId(buffer.readInt()));
			}

//
//			Effect outputEffect = Effect.get(buffer.readInt());
//			int baseDuration = buffer.readInt();

			return this.factory.create(recipeId, input, outputEffectList, inputEffectList, syphon, ticks, minimumTier);
		} catch (Exception e)
		{
			throw e;
		}
	}

	@Override
	public void toNetwork(@Nonnull FriendlyByteBuf buffer, @Nonnull RECIPE recipe)
	{
		try
		{
			recipe.write(buffer);
		} catch (Exception e)
		{
			throw e;
		}
	}

	@FunctionalInterface
	public interface IFactory<RECIPE extends RecipePotionTransform>
	{
		RECIPE create(ResourceLocation id, List<Ingredient> input, List<Pair<MobEffect, Integer>> outputEffectList, List<MobEffect> inputEffectList, int syphon, int ticks, int minimumTier);
	}
}
