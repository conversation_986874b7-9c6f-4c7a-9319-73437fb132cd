package wayoftime.bloodmagic.common.recipe.serializer;

import net.minecraft.core.RegistryAccess;
import net.minecraft.world.inventory.CraftingContainer;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.crafting.RecipeSerializer;
import net.minecraft.world.item.crafting.RecipeType;
import net.minecraft.world.item.crafting.CustomRecipe;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.level.Level;
import net.minecraftforge.common.crafting.IShapedRecipe;

//public class TestSpecialRecipe extends CustomRecipe implements IShapedRecipe<CraftingContainer>
//{
//
//	public TestSpecialRecipe(ResourceLocation idIn)
//	{
//		super(idIn);
//		System.out.println("XYZ");
//		// TODO Auto-generated constructor stub
//	}
//
//	@Override
//	public boolean matches(CraftingContainer inv, Level worldIn)
//	{
//		// TODO Auto-generated method stub
//		return false;
//	}
//
//	@Override
//	public ItemStack assemble(CraftingContainer p_44001_, RegistryAccess p_267165_) {
//		// TODO Auto-generated method stub
//		return null;
//	}
//
//	@Override
//	public boolean canCraftInDimensions(int width, int height)
//	{
//		// TODO Auto-generated method stub
//		return false;
//	}
//
//	@Override
//	public RecipeSerializer<?> getSerializer()
//	{
//		// TODO Auto-generated method stub
//		return null;
//	}
//
//	@Override
//	public RecipeType<?> getType()
//	{
//		// TODO Auto-generated method stub
//		return null;
//	}
//
//	@Override
//	public int getRecipeWidth()
//	{
//		// TODO Auto-generated method stub
//		return 0;
//	}
//
//	@Override
//	public int getRecipeHeight()
//	{
//		// TODO Auto-generated method stub
//		return 0;
//	}
//
//}
