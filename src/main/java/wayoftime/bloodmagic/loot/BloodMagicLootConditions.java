package wayoftime.bloodmagic.loot;

import net.minecraft.world.level.storage.loot.predicates.LootItemConditionType;
import net.minecraft.world.level.storage.loot.predicates.MatchTool;
import net.minecraft.core.Registry;
import wayoftime.bloodmagic.BloodMagic;

public class BloodMagicLootConditions
{
//	public static final LootItemConditionType INVERTED = Registry.register(Registry.LOOT_CONDITION_TYPE, BloodMagic.rl("testing"), new LootItemConditionType(new MatchTool.Serializer()));
//
//	static
//	{
//
//	}
}
