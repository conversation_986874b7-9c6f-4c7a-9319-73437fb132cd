{"type": "minecraft:chest", "pools": [{"bonus_rolls": 0.0, "entries": [{"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 20.0, "min": 8.0}, "function": "minecraft:set_count"}], "name": "minecraft:raw_copper", "quality": -4, "weight": 15}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 8.0, "min": 4.0}, "function": "minecraft:set_count"}], "name": "minecraft:raw_iron", "quality": 1, "weight": 20}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 7.0, "min": 3.0}, "function": "minecraft:set_count"}], "name": "minecraft:raw_gold", "quality": 2, "weight": 25}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 6.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "minecraft:diamond", "quality": 4, "weight": 5}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 8.0, "min": 3.0}, "function": "minecraft:set_count"}], "name": "minecraft:emerald", "quality": 5, "weight": 3}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 6.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:strong_tau", "quality": 3, "weight": 6}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 4.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:bow_power_anointment_2"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 4.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:fortune_anointment_2"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 4.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:hidden_knowledge_anointment_2"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 4.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:holy_water_anointment_2"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 4.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:looting_anointment_2"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 4.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:melee_anointment_2"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 4.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:quick_draw_anointment_2"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 4.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:silk_touch_anointment_l"}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 4.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:smelting_anointment_l"}, {"type": "minecraft:item", "functions": [{"add": false, "count": 1.0, "function": "minecraft:set_count"}, {"function": "minecraft:enchant_with_levels", "levels": {"type": "minecraft:uniform", "max": 39.0, "min": 20.0}, "treasure": true}, {"add": false, "damage": {"type": "minecraft:uniform", "max": 1.0, "min": 0.7}, "function": "minecraft:set_damage"}], "name": "minecraft:diamond_boots", "quality": 2, "weight": 3}, {"type": "minecraft:item", "functions": [{"add": false, "count": 1.0, "function": "minecraft:set_count"}, {"function": "minecraft:enchant_with_levels", "levels": {"type": "minecraft:uniform", "max": 39.0, "min": 20.0}, "treasure": true}, {"add": false, "damage": {"type": "minecraft:uniform", "max": 1.0, "min": 0.7}, "function": "minecraft:set_damage"}], "name": "minecraft:diamond_chestplate", "quality": 2, "weight": 3}, {"type": "minecraft:item", "functions": [{"add": false, "count": 1.0, "function": "minecraft:set_count"}, {"function": "minecraft:enchant_with_levels", "levels": {"type": "minecraft:uniform", "max": 39.0, "min": 20.0}, "treasure": true}, {"add": false, "damage": {"type": "minecraft:uniform", "max": 1.0, "min": 0.7}, "function": "minecraft:set_damage"}], "name": "minecraft:diamond_helmet", "quality": 2, "weight": 3}, {"type": "minecraft:item", "functions": [{"add": false, "count": 1.0, "function": "minecraft:set_count"}, {"function": "minecraft:enchant_with_levels", "levels": {"type": "minecraft:uniform", "max": 39.0, "min": 20.0}, "treasure": true}, {"add": false, "damage": {"type": "minecraft:uniform", "max": 1.0, "min": 0.7}, "function": "minecraft:set_damage"}], "name": "minecraft:diamond_leggings", "quality": 2, "weight": 3}, {"type": "minecraft:item", "functions": [{"add": false, "count": 1.0, "function": "minecraft:set_count"}, {"function": "minecraft:enchant_with_levels", "levels": {"type": "minecraft:uniform", "max": 39.0, "min": 20.0}, "treasure": true}, {"add": false, "damage": {"type": "minecraft:uniform", "max": 0.8, "min": 0.5}, "function": "minecraft:set_damage"}], "name": "minecraft:iron_boots", "weight": 4}, {"type": "minecraft:item", "functions": [{"add": false, "count": 1.0, "function": "minecraft:set_count"}, {"function": "minecraft:enchant_with_levels", "levels": {"type": "minecraft:uniform", "max": 39.0, "min": 20.0}, "treasure": true}, {"add": false, "damage": {"type": "minecraft:uniform", "max": 0.8, "min": 0.5}, "function": "minecraft:set_damage"}], "name": "minecraft:iron_chestplate", "weight": 4}, {"type": "minecraft:item", "functions": [{"add": false, "count": 1.0, "function": "minecraft:set_count"}, {"function": "minecraft:enchant_with_levels", "levels": {"type": "minecraft:uniform", "max": 39.0, "min": 20.0}, "treasure": true}, {"add": false, "damage": {"type": "minecraft:uniform", "max": 0.8, "min": 0.5}, "function": "minecraft:set_damage"}], "name": "minecraft:iron_helmet", "weight": 4}, {"type": "minecraft:item", "functions": [{"add": false, "count": 1.0, "function": "minecraft:set_count"}, {"function": "minecraft:enchant_with_levels", "levels": {"type": "minecraft:uniform", "max": 39.0, "min": 20.0}, "treasure": true}, {"add": false, "damage": {"type": "minecraft:uniform", "max": 0.8, "min": 0.5}, "function": "minecraft:set_damage"}], "name": "minecraft:iron_leggings", "weight": 4}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 4.0, "min": 1.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:sand_netherite", "quality": 3, "weight": 4}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 5.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:sand_hellforged", "quality": 6, "weight": 3}, {"type": "minecraft:item", "functions": [{"damage": {"type": "minecraft:uniform", "max": 50.0, "min": 20.0}, "function": "bloodmagic:set_will_range"}], "name": "bloodmagic:<PERSON><PERSON><PERSON><PERSON><PERSON>", "weight": 8}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 5.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:corrosivecrystal", "weight": 2}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 5.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:steadfastcrystal", "weight": 2}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 5.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:vengefulcrystal", "weight": 2}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 5.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:destructivecrystal", "weight": 2}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 5.0, "min": 2.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:defaultcrystal", "weight": 2}, {"type": "minecraft:item", "functions": [{"add": false, "count": {"type": "minecraft:uniform", "max": 12.0, "min": 7.0}, "function": "minecraft:set_count"}], "name": "bloodmagic:syntheticpoint", "weight": 5}], "rolls": {"type": "minecraft:uniform", "max": 6.0, "min": 3.0}}], "random_sequence": "bloodmagic:chests/standard_dungeon/decent_loot"}