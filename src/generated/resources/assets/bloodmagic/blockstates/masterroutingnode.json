{"multipart": [{"apply": {"model": "bloodmagic:block/routing/modelmasterroutingnodecore"}}, {"apply": {"model": "bloodmagic:block/routing/modelmasterroutingnodebase"}, "when": {"down": "true"}}, {"apply": {"model": "bloodmagic:block/routing/modelmasterroutingnodebase", "x": 180}, "when": {"up": "true"}}, {"apply": {"model": "bloodmagic:block/routing/modelmasterroutingnodebase", "x": 270}, "when": {"north": "true"}}, {"apply": {"model": "bloodmagic:block/routing/modelmasterroutingnodebase", "x": 90}, "when": {"south": "true"}}, {"apply": {"model": "bloodmagic:block/routing/modelmasterroutingnodebase", "x": 90, "y": 90}, "when": {"west": "true"}}, {"apply": {"model": "bloodmagic:block/routing/modelmasterroutingnodebase", "x": 90, "y": 270}, "when": {"east": "true"}}]}