{"multipart": [{"apply": {"model": "bloodmagic:block/routing/routingnodecore"}}, {"apply": {"model": "bloodmagic:block/routing/routingnodebase"}, "when": {"down": "true"}}, {"apply": {"model": "bloodmagic:block/routing/routingnodebase", "x": 180}, "when": {"up": "true"}}, {"apply": {"model": "bloodmagic:block/routing/routingnodebase", "x": 270}, "when": {"north": "true"}}, {"apply": {"model": "bloodmagic:block/routing/routingnodebase", "x": 90}, "when": {"south": "true"}}, {"apply": {"model": "bloodmagic:block/routing/routingnodebase", "x": 90, "y": 90}, "when": {"west": "true"}}, {"apply": {"model": "bloodmagic:block/routing/routingnodebase", "x": 90, "y": 270}, "when": {"east": "true"}}]}