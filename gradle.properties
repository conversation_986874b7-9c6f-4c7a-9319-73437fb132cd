# Sets default memory used for gradle commands. Can be overridden by user or command line properties.
# This is required to provide enough memory for the Minecraft decompilation process.
org.gradle.jvmargs=-Xmx3G
org.gradle.daemon=false

minecraft_version=1.20.1
minecraft_version_range=[1.20.1,1.21)
# The Forge version must agree with the Minecraft version to get a valid artifact
forge_version=47.1.0
# The Forge version range can use any version of Forge as bounds or match the loader version range
forge_version_range=[47,)
# The loader version range can only use the major version of Forge/FML as bounds
loader_version_range=[47,)
mapping_channel=official
mapping_version=1.20.1

mod_id=bloodmagic
mod_version=3.3.3-45
mod_name=Blood Magic
# The license of the mod. Review your options at https://choosealicense.com/. All Rights Reserved is the default.
mod_license=CC BY 4.0
# The mod version. See https://semver.org/
# The group ID for the mod. It is only important when publishing as an artifact to a Maven repository.
# This should match the base package used for the mod sources.
# See https://maven.apache.org/guides/mini/guide-naming-conventions.html
mod_group_id=com.example.examplemod
# The authors of the mod. This is a simple text string that is used for display purposes in the mod list.
mod_authors=WayofTime
mod_description=Gruesome? Probably. Worth it? Definitely!

#Mod dependencies
mixin_version=0.8.5
mixin_extras=0.2.0
jei_version=*********
curios_version=5.3.4*****.1
patchouli_version=1.20.1-80-FORGE
mysticalag_version=7.0.7
cucumber_version=7.0.7